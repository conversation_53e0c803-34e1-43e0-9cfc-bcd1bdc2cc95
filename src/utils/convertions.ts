import { Priority } from "../interfaces/complaint";
import { Status } from "../interfaces/maintenance-issue-report";
import { InfractionSeverity } from "../interfaces/infraction";
import { ReservationStatus } from "../interfaces/reservation";
import { theme } from "../theme";

interface InfractionStatusData {
  status: "Menor" | "Moderada" | "Grave";
  backgroundColor: string;
  borderColor: string;
}

export const getProirity = (priority: Priority) => {
  if (priority === Priority.HIGH) return "Alta";
  if (priority === Priority.MEDIUM) return "Media";
  if (priority === Priority.LOW) return "Baja";
};

export const getStatus = (status: Status) => {
  if (status === Status.RESOLVED) return "Resuelta";
  if (status === Status.OPEN) return "Abierta";
  if (status === Status.IN_PROGRESS) return "En progreso";
};

export const getInfractionStatus = (
  status: InfractionSeverity
): InfractionStatusData => {
  if (status === InfractionSeverity.MINOR)
    return {
      status: "Menor",
      backgroundColor: theme.colors.yellow.light,
      borderColor: theme.colors.yellow.dark,
    };
  if (status === InfractionSeverity.MODERATE)
    return {
      status: "Moderada",
      backgroundColor: theme.colors.orange.light,
      borderColor: theme.colors.orange.dark,
    };
  if (status === InfractionSeverity.SEVERE)
    return {
      status: "Grave",
      backgroundColor: theme.colors.red.light,
      borderColor: theme.colors.red.dark,
    };
  return {
    status: "Menor",
    backgroundColor: theme.colors.error,
    borderColor: theme.colors.error,
  };
};

export const getReservationStatus = (
  reservationStatus: ReservationStatus
): string => {
  if (reservationStatus === ReservationStatus.APPROVED) return "Aprobado";
  if (reservationStatus === ReservationStatus.PENDING) return "Pendiente";
  if (reservationStatus === ReservationStatus.REJECTED) return "Rechazado";
  return "";
};
