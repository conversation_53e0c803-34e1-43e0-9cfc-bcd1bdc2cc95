import { Reservation } from "./reservation";
import { User } from "./user";
import { MaintenanceIssueReport } from "./maintenance-issue-report";
import { Infraction } from "./infraction";
import { Visit } from "./visit";
import { ParkingSpot } from "./parking-spot";
import { Pet } from "./pet";
import { Vehicle } from "./vehicle";
import { Tag } from "./tag";
import { Fine } from "./fine";
import { MonthlyMaintenanceCharge } from "./monthly-maintenance-charge";
import { Complaint } from "./complaint";
import { Role } from "./role";
import { Property } from "./property";
import { Announcement } from "./announcement";
import { Facility } from "./facility";

export interface Me {
  id: User["id"];
  email: User["email"];
  firstName: User["firstName"];
  paternalLastName: User["paternalLastName"];
  maternalLastName: User["maternalLastName"];
  phone: User["phone"];
  properties: PartialProperty[];
  announcements: PartialAnnouncement[];
  complaints: Complaint[];
  reservations: Reservation[];
  maintenanceIssueReports: PartialMaintenanceIssueReport[];
  visits: Visit[];
  requestedReservations: Reservation[];
}

interface PartialProperty {
  id: Property["id"];
  type: Property["type"];
  address: Property["address"];
  residents: PartialResident[];
  reservations: Reservation[];
  maintenanceIssueReports: PartialMaintenanceIssueReport[];
  infractions: Infraction[];
  visits: Visit[];
  parkingSpots: ParkingSpot[];
  pets: Pet[];
  vehicles: Vehicle[];
  tags: Tag[];
  fines: Fine[];
  monthlyMaintenanceCharges: MonthlyMaintenanceCharge[];
  complaints: Complaint[];
}

interface PartialRole {
  id: Role["id"];
  name: Role["name"];
}

export interface PartialResident {
  id: User["id"];
  email: User["email"];
  firstName: User["firstName"];
  paternalLastName: User["paternalLastName"];
  maternalLastName: User["maternalLastName"];
  phone: User["phone"];
  roles: PartialRole[];
}

export interface PartialAnnouncement {
  id: Announcement["id"];
  title: Announcement["title"];
  message: Announcement["message"];
  imageUrl: Announcement["imageUrl"];
  createdAt: Announcement["createdAt"];
  role: PartialRole;
  images: AnnouncementImage[];
}

export interface AnnouncementImage {
  id: string;
  path: string;
  announcementId: string;
  createdAt: Date;
}

export interface PartialReservation {
  id: Reservation["id"];
  amountOfPeople: Reservation["amountOfPeople"];
  startDateTime: Reservation["startDateTime"];
  endDateTime: Reservation["endDateTime"];
  status: Reservation["status"];
  createdAt: Reservation["createdAt"];
  updatedAt: Reservation["updatedAt"];
  authorizedAt: Reservation["authorizedAt"];
  deniedAt: Reservation["deniedAt"];
  deniedReason: Reservation["deniedReason"];
  facility: PartialFacility;
}

interface PartialFacility {
  name: Facility["name"];
}

export interface PartialMaintenanceIssueReport {
  id: MaintenanceIssueReport["id"];
  description: MaintenanceIssueReport["description"];
  status: MaintenanceIssueReport["status"];
  createdAt: MaintenanceIssueReport["createdAt"];
}

export interface PartialComplaint {
  id: Complaint["id"];
  status: Complaint["status"];
  detail: Complaint["detail"];
  priority: Complaint["priority"];
}
