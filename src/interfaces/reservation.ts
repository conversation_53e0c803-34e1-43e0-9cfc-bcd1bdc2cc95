import { Facility } from "./facility";
import { Property } from "./property";
import { User } from "./user";

export interface Reservation {
  id: string;
  amountOfPeople: number;
  startDateTime: string;
  endDateTime: string;
  status: ReservationStatus;
  createdAt: string;
  updatedAt: string;
  authorizedAt?: string;
  deniedAt?: string;
  deniedReason?: string;
  propertyId: Property["id"];
  facilityId: Facility["id"];
  facility: Facility;
  requestedBy: User["id"];
}

export enum ReservationStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}
