import { User } from "./user";
import { ParkingSpot } from "./parking-spot";
import { Reservation } from "./reservation";
import { Rental } from "./rental";
import { Infraction } from "./infraction";
import { Payment } from "./payment";
import { Visit } from "./visit";
import { Pet } from "./pet";
import { Vehicle } from "./vehicle";
import { Tag } from "./tag";
import { Fine } from "./fine";
import { Complaint } from "./complaint";
import { MonthlyMaintenanceCharge } from "./monthly-maintenance-charge";

export interface Property {
  id: string;
  address: string;
  type: PropertyType;

  createdAt: string;
  updatedAt: string;

  residents: User[];
  reservations: Reservation[];
  rentals: Rental[];
  infractions: Infraction[];
  payments: Payment[];
  visits: Visit[];
  parkingSpots: ParkingSpot[];
  pets: Pet[];
  vehicles: Vehicle[];
  tags: Tag[];
  fines: Fine[];
  complaints: Complaint[];
  monthlyMaintenanceCharges: MonthlyMaintenanceCharge[];
}

export enum PropertyType {
  HOUSE = "HOUSE",
  DEPARTMENT = "DEPARTMENT",
}
