import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { FacilitiesScreen } from "../../screens/Facilities/FacilitiesScreen";
import { FacilityScreen } from "../../screens/Facilities/FacilityScreen";
import { CreateReservationScreen } from "../../screens/Reservations/CreateReservationScreen";
import { FacilitiesStackParamList } from "../types";
import { FACILITIES_SCREENS } from "../constants";

const Stack = createNativeStackNavigator<FacilitiesStackParamList>();

export const FacilitiesStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={FACILITIES_SCREENS.FACILITIES_LIST}
        component={FacilitiesScreen}
        options={{
          title: "Amenidades",
          headerShown: false,
          headerBackVisible: true,
        }}
      />
      <Stack.Screen
        name={FACILITIES_SCREENS.FACILITY_DETAIL}
        component={FacilityScreen}
        options={{
          title: "Detalle de Amenidad",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={FACILITIES_SCREENS.CREATE_RESERVATION}
        component={CreateReservationScreen}
        options={{
          title: "Crear Reservación",
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
