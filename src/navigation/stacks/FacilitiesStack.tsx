import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { FacilitiesScreen } from "../../screens/Facilities/FacilitiesScreen";
import { Facility } from "../../interfaces/facility";
import { RouteProp } from "@react-navigation/native";
import { FacilityScreen } from "../../screens/Facilities/FacilityScreen";

export const facilitiesNavigation = {
  facilities: "facilities",
  facility: "facility",
} as const;

export type FacilitiesStackParamList = {
  [facilitiesNavigation.facilities]: { isLoading?: boolean };
  [facilitiesNavigation.facility]: {
    facility: Facility;
  };
};

export type FacilityRouteType = RouteProp<
  FacilitiesStackParamList,
  typeof facilitiesNavigation.facility
>;

const Stack = createNativeStackNavigator<FacilitiesStackParamList>();

export const FacilitiesStack: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name={facilitiesNavigation.facilities}
        component={FacilitiesScreen}
      />
      <Stack.Screen
        name={facilitiesNavigation.facility}
        component={FacilityScreen}
      />
    </Stack.Navigator>
  );
};
