import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { PropertyScreen } from "../../screens/Property/PropertyScreen";

export type PropertiesStackParamList = {
  PropertiesList: undefined;
};

const Stack = createNativeStackNavigator();

export const PropertiesStack: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="PropertiesList" component={PropertyScreen} />
    </Stack.Navigator>
  );
};
