import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { PropertyScreen } from "../../screens/Property/PropertyScreen";
import { PropertyStackParamList } from "../types";
import { PROPERTY_SCREENS } from "../constants";

const Stack = createNativeStackNavigator<PropertyStackParamList>();

export const PropertyStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_DETAIL}
        component={PropertyScreen}
        options={{
          title: "Mi Propiedad",
        }}
      />
    </Stack.Navigator>
  );
};

// Export for backward compatibility
export const PropertiesStack = PropertyStack;
