import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { Facility } from "../../interfaces/facility";
import { RouteProp } from "@react-navigation/native";
import { ReservationScreen } from "../../screens/Reservations/ReservationScreen";

export const reservationNavigation = {
  createReservation: "createReservation",
} as const;

export type ReservationsStackParamList = {
  [reservationNavigation.createReservation]: {
    id: Facility["id"];
    name: Facility["name"];
    maxAmountOfPeople: Facility["maxAmountOfPeople"];
    maxTimeOfStay: Facility["maxTimeOfStay"];
    open: Facility["open"];
    close: Facility["close"];
    selectedDate: string;
  };
};

export type CreateReservationRouteType = RouteProp<
  ReservationsStackParamList,
  typeof reservationNavigation.createReservation
>;

const Stack = createNativeStackNavigator<ReservationsStackParamList>();

export const ReservationStack: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name={reservationNavigation.createReservation}
        component={ReservationScreen}
      />
    </Stack.Navigator>
  );
};
