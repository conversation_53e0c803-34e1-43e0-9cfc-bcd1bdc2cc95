import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { DashboardScreen } from "../../screens/Dashboard/DashboardScreen";
import { CreateComplaintScreen } from "../../screens/ComplaintScreen/CreateComplaintScreen";
import { EmergencyNumbersScreen } from "../../screens/Dashboard/EmergencyNumbersScreen";

export const dashboardNavigation = {
  dashboard: "dashboard",
  createComplaint: "createComplaint",
  emergencyNumbersModal: "emergencyNumbersModal",
} as const;

export type DashboardStackParamList = {
  [dashboardNavigation.dashboard]: undefined;
  [dashboardNavigation.createComplaint]: { complaintId?: string }; // ejemplo con params
  [dashboardNavigation.emergencyNumbersModal]: undefined;
};

const Stack = createNativeStackNavigator<DashboardStackParamList>();

export const MainStack: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name={dashboardNavigation.dashboard}
        component={DashboardScreen}
      />
      <Stack.Screen
        name={dashboardNavigation.createComplaint}
        component={CreateComplaintScreen}
      />
      <Stack.Screen
        name={dashboardNavigation.emergencyNumbersModal}
        component={EmergencyNumbersScreen}
        options={{
          presentation: "modal",
          headerShown: true,
          title: "Números de emergencia",
        }}
      />
    </Stack.Navigator>
  );
};
