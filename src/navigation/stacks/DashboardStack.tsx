import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { DashboardScreen } from "../../screens/Dashboard/DashboardScreen";
import { CreateComplaintScreen } from "../../screens/ComplaintScreen/CreateComplaintScreen";
import { CreateMaintenanceReportScreen } from "../../screens/MaintenanceReports/CreateMaintenanceReportScreen";
import { EmergencyNumbersScreen } from "../../screens/Dashboard/EmergencyNumbersScreen";
import { DashboardStackParamList } from "../types";
import { DASHBOARD_SCREENS } from "../constants";

const Stack = createNativeStackNavigator<DashboardStackParamList>();

export const DashboardStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={DASHBOARD_SCREENS.DASHBOARD}
        component={DashboardScreen}
        options={{
          title: "Dashboard",
        }}
      />
      <Stack.Screen
        name={DASHBOARD_SCREENS.CREATE_COMPLAINT}
        component={CreateComplaintScreen}
        options={{
          title: "Crear Queja",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={DASHBOARD_SCREENS.CREATE_MAINTENANCE_REPORT}
        component={CreateMaintenanceReportScreen}
        options={{
          title: "Reporte de Mantenimiento",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={DASHBOARD_SCREENS.EMERGENCY_NUMBERS}
        component={EmergencyNumbersScreen}
        options={{
          presentation: "modal",
          headerShown: true,
          title: "Números de emergencia",
        }}
      />
    </Stack.Navigator>
  );
};

// Export for backward compatibility
export const MainStack = DashboardStack;
