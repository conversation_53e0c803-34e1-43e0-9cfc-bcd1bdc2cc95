import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { PaymentsScreen } from "../../screens/Payments/PaymentsScreen";

export type PaymentsStackParamList = {
  PaymentList: undefined;
};

const Stack = createNativeStackNavigator();

export const PaymentsStack: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="PaymentList"
        component={PaymentsScreen}
        options={{ title: "Pagos" }}
      />
    </Stack.Navigator>
  );
};
