import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { PaymentsScreen } from "../../screens/Payments/PaymentsScreen";
import { PaymentsStackParamList } from "../types";
import { PAYMENTS_SCREENS } from "../constants";

const Stack = createNativeStackNavigator<PaymentsStackParamList>();

export const PaymentsStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={PAYMENTS_SCREENS.PAYMENTS_LIST}
        component={PaymentsScreen}
        options={{
          title: "Pagos",
        }}
      />
      {/* Future payment detail screen can be added here */}
      {/* <Stack.Screen
        name={PAYMENTS_SCREENS.PAYMENT_DETAIL}
        component={PaymentDetailScreen}
        options={{
          title: "Detalle de Pago",
          headerShown: true,
        }}
      /> */}
    </Stack.Navigator>
  );
};
