import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { AccountScreen } from "../../screens/Account/AccountScreen";

export type AccountStackParamList = {
  Account: undefined;
};

const Stack = createNativeStackNavigator();

export const AccountStack: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Account" component={AccountScreen} />
    </Stack.Navigator>
  );
};
