import React from "react";
import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from "@react-navigation/bottom-tabs";

import { PropertiesStack } from "./stacks/PropertyStack";
import { AccountStack } from "./stacks/AccountStack";
import { PaymentsStack } from "./stacks/PaymentsStack";
import { MainStack } from "./stacks/DashboardStack";
import {
  getFocusedRouteNameFromRoute,
  ParamListBase,
  RouteProp,
} from "@react-navigation/native";
import { NO_TABBAR_ROUTES } from "./routes";
import { TabBarIcon, TabBarIconProps } from "../components/TabBarIcon";
import { FacilitiesStack } from "./stacks/FacilitiesStack";
import { theme } from "../theme";
import { LinearGradient } from "expo-linear-gradient";
import { useIsFetching } from "@tanstack/react-query";

export const tabNavigation = {
  dashboard: "Dashboard",
  facilities: "Amenidades",
  payments: "Pagos",
  profile: "Perfil",
  account: "Cuenta",
  createComplaint: "CreateComplaint",
  property: "Propiedad",
} as const;

export type MainStackParamList = {
  [tabNavigation.dashboard]: undefined;
  [tabNavigation.facilities]: undefined;
  [tabNavigation.payments]: undefined;
  [tabNavigation.profile]: undefined;
  [tabNavigation.account]: undefined;
  [tabNavigation.createComplaint]: undefined;
  [tabNavigation.property]: undefined;
};

const Tab = createBottomTabNavigator<MainStackParamList>();

const getScreenOptions = (
  route: RouteProp<ParamListBase, string>,
  isLoading: boolean
): BottomTabNavigationOptions => {
  const routeName = getFocusedRouteNameFromRoute(route) ?? "";

  const shouldHideTabBar = NO_TABBAR_ROUTES.includes(routeName) || isLoading;

  return {
    headerShown: true,
    title: "",
    headerBackground: () => (
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      />
    ),
    tabBarStyle: shouldHideTabBar
      ? { display: "none" }
      : {
          backgroundColor: theme.colors.primaryLight,
          height: 70,
          position: "absolute",
          left: 20,
          right: 20,
          borderTopRightRadius: 32,
          borderTopLeftRadius: 32,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.25,
          shadowRadius: 3.5,
          elevation: 5,
        },
    tabBarIcon: ({ color, size, focused }: TabBarIconProps) => (
      <TabBarIcon
        color={color}
        size={size}
        routeName={route.name}
        focused={focused}
      />
    ),
    tabBarActiveTintColor: theme.colors.primaryDark,
    tabBarInactiveTintColor: theme.colors.gray500,
  };
};

export const TabNavigator: React.FC = () => {
  const isFetching = useIsFetching();
  return (
    <Tab.Navigator
      screenOptions={({ route }: any) =>
        getScreenOptions(route, isFetching > 0)
      }
      initialRouteName={tabNavigation.dashboard}
    >
      <Tab.Screen name={tabNavigation.facilities} component={FacilitiesStack} />
      <Tab.Screen name={tabNavigation.property} component={PropertiesStack} />
      <Tab.Screen name={tabNavigation.dashboard} component={MainStack} />
      <Tab.Screen name={tabNavigation.payments} component={PaymentsStack} />
      <Tab.Screen name={tabNavigation.account} component={AccountStack} />
    </Tab.Navigator>
  );
};
