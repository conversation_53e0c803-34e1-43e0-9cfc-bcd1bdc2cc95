import React from "react";
import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from "@react-navigation/bottom-tabs";
import {
  getFocusedRouteNameFromRoute,
  ParamListBase,
  RouteProp,
} from "@react-navigation/native";
import { useIsFetching } from "@tanstack/react-query";
import { LinearGradient } from "expo-linear-gradient";

// Stacks
import { DashboardStack } from "./stacks/DashboardStack";
import { FacilitiesStack } from "./stacks/FacilitiesStack";
import { PropertyStack } from "./stacks/PropertyStack";
import { PaymentsStack } from "./stacks/PaymentsStack";
import { AccountStack } from "./stacks/AccountStack";

// Types and Constants
import { MainTabParamList } from "./types";
import { TAB_NAMES, HIDDEN_TAB_BAR_ROUTES } from "./constants";

// Components
import { TabBarIcon } from "../components/TabBarIcon";
import { theme } from "../theme";

const Tab = createBottomTabNavigator<MainTabParamList>();

const getScreenOptions = (
  route: RouteProp<ParamListBase, string>,
  isLoading: boolean
): BottomTabNavigationOptions => {
  const routeName = getFocusedRouteNameFromRoute(route) ?? "";

  const shouldHideTabBar =
    HIDDEN_TAB_BAR_ROUTES.includes(routeName as any) ?? isLoading;

  return {
    headerShown: true,
    title: "",
    headerBackground: () => (
      <LinearGradient
        colors={["#1B4959", "#DFD6C6"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 2.5, y: 0 }}
        style={{ flex: 1 }}
      />
    ),
    tabBarStyle: shouldHideTabBar
      ? { display: "none" }
      : {
          backgroundColor: theme.colors.primaryLight,
          height: 85,
          position: "absolute",
          left: theme.spacing.md,
          right: theme.spacing.md,
          paddingTop: theme.spacing.md,
          borderRadius: theme.radii.xl,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.25,
          shadowRadius: 3.5,
          elevation: 5,
        },
    tabBarIcon: ({ color, size, focused }) => (
      <TabBarIcon
        color={color}
        size={size}
        routeName={route.name}
        focused={focused}
      />
    ),
    tabBarActiveTintColor: theme.colors.primaryDark,
    tabBarInactiveTintColor: theme.colors.gray500,
  };
};

export const TabNavigator: React.FC = () => {
  const isFetching = useIsFetching();

  return (
    <Tab.Navigator
      screenOptions={({ route }: any) =>
        getScreenOptions(route, isFetching > 0)
      }
      initialRouteName={TAB_NAMES.DASHBOARD}
    >
      <Tab.Screen name={TAB_NAMES.FACILITIES} component={FacilitiesStack} />
      <Tab.Screen name={TAB_NAMES.PROPERTY} component={PropertyStack} />
      <Tab.Screen name={TAB_NAMES.DASHBOARD} component={DashboardStack} />
      <Tab.Screen name={TAB_NAMES.PAYMENTS} component={PaymentsStack} />
      <Tab.Screen name={TAB_NAMES.ACCOUNT} component={AccountStack} />
    </Tab.Navigator>
  );
};
