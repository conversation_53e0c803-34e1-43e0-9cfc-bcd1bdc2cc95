import { NavigationContainer } from "@react-navigation/native";
import { AuthNavigator } from "./AuthNavigator";
import { useAuthContext } from "../context/AuthContext";
import { LoadingScreen } from "../screens/Loading/LoadingScreen";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { MainNavigator } from "./MainNavigator";

const Stack = createNativeStackNavigator();

export const AppNavigator: React.FC = () => {
  const { user, isLoading } = useAuthContext();
  if (isLoading) return <LoadingScreen />;

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user ? <MainNavigator /> : <AuthNavigator />}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
