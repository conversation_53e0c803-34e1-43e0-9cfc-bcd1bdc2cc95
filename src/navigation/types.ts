import { NavigatorScreenParams, RouteProp } from "@react-navigation/native";
import { Facility } from "../interfaces/facility";

// Stack Parameter Lists
export type DashboardStackParamList = {
  Dashboard: undefined;
  CreateComplaint: { complaintId?: string };
  CreateMaintenanceReport: { reportId?: string };
  EmergencyNumbers: undefined;
};

export type FacilitiesStackParamList = {
  FacilitiesList: { isLoading?: boolean };
  FacilityDetail: { facility: Facility };
  CreateReservation: {
    id: Facility["id"];
    name: Facility["name"];
    maxAmountOfPeople: Facility["maxAmountOfPeople"];
    maxTimeOfStay: Facility["maxTimeOfStay"];
    open: Facility["open"];
    close: Facility["close"];
    selectedDate: string;
  };
};

export type PropertyStackParamList = {
  PropertyDetail: undefined;
};

export type PaymentsStackParamList = {
  PaymentsList: undefined;
  PaymentDetail: { paymentId: string };
};

export type AccountStackParamList = {
  Profile: undefined;
  Settings: undefined;
  EditProfile: undefined;
};

// Main Tab Navigator Parameter List
export type MainTabParamList = {
  DashboardTab: NavigatorScreenParams<DashboardStackParamList>;
  FacilitiesTab: NavigatorScreenParams<FacilitiesStackParamList>;
  PropertyTab: NavigatorScreenParams<PropertyStackParamList>;
  PaymentsTab: NavigatorScreenParams<PaymentsStackParamList>;
  AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

// Root Stack Parameter List
export type RootStackParamList = {
  Auth: undefined;
  Main: NavigatorScreenParams<MainTabParamList>;
};

// Navigation Props Types
export type TabNavigationProp = any; // Will be properly typed with navigation hook
export type StackNavigationProp = any; // Will be properly typed with navigation hook

// Route Props Types
export type TabRouteProp = any; // Will be properly typed with route hook
export type StackRouteProp = any; // Will be properly typed with route hook

// Specific Route Types
export type FacilityRouteType = RouteProp<
  FacilitiesStackParamList,
  "FacilityDetail"
>;

export type CreateReservationRouteType = RouteProp<
  FacilitiesStackParamList,
  "CreateReservation"
>;
