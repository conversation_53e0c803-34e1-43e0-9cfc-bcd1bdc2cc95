import { Ionicons } from "@expo/vector-icons";
import { ROUTES } from "../navigation/routes";
import { StyleSheet, View } from "react-native";
import { theme } from "../theme";

export interface TabBarIconProps {
  color: string;
  size: number;
  routeName: string;
  focused: boolean;
}

export const TabBarIcon: React.FC<TabBarIconProps> = ({
  color,
  size,
  routeName,
  focused,
}) => {
  let iconName: keyof typeof Ionicons.glyphMap;

  if (routeName === ROUTES.DASHBOARD) iconName = "grid-outline";
  else if (routeName === ROUTES.FACILITES) iconName = "albums-outline";
  else if (routeName === ROUTES.PAYMENTS) iconName = "card";
  else if (routeName === ROUTES.HOME) iconName = "home";
  else iconName = "person";

  return (
    <View style={focused ? styles.focusedIconContainer : null}>
      <Ionicons name={iconName} size={size} color={color} />
    </View>
  );
};

const styles = StyleSheet.create({
  focusedIconContainer: {
    height: 70,
    width: 70,
    backgroundColor: theme.colors.gold,
    borderRadius: 20,
    padding: theme.spacing.xs,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
  },
});
