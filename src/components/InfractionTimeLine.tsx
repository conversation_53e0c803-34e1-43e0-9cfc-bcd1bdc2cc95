import { View, Text, StyleSheet } from "react-native";

interface InfractionTimelineItemProps {
  description: string;
  date: string;
  severity: "Menor" | "Moderada" | "Grave";
}

const severityDotColor = {
  Grave: "#DC2626",
  Moderada: "#F59E0B",
  Menor: "#10B981",
};

export const InfractionTimelineItem: React.FC<InfractionTimelineItemProps> = ({
  description,
  date,
  severity,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.timeline}>
        <View
          style={[styles.dot, { backgroundColor: severityDotColor[severity] }]}
        />
        <View style={styles.line} />
      </View>
      <View style={styles.content}>
        <Text style={styles.description}>{description}</Text>
        <Text style={styles.date}>{date}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginBottom: 20,
  },
  timeline: {
    alignItems: "center",
    marginRight: 12,
    width: 20,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginTop: 2,
  },
  line: {
    flex: 1,
    width: 2,
    backgroundColor: "#E5E7EB",
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  description: {
    fontWeight: "bold",
    color: "#111827",
  },
  date: {
    fontSize: 12,
    color: "#6B7280",
  },
});
