import React from "react";
import { View, StyleSheet } from "react-native";
import { Title } from "./main";
import { getGreeting } from "../utils/date-time.utils";

interface CustomHeaderProps {
  firstName: string;
}

export const CustomHeader: React.FC<CustomHeaderProps> = ({ firstName }) => {
  return (
    <View style={styles.container}>
      <Title size="xl" style={styles.text}>
        {getGreeting()},
      </Title>
      <Title size="xl" style={styles.text}>
        {firstName}
      </Title>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    color: "white",
  },
});
