import React from "react";
import { ImageBackground, StyleSheet, Text, View } from "react-native";
import { theme } from "../theme";
import { Pill } from "./Pill";

interface InfoWithImageProps {
  title: string;
  description: string;
  imageUrl: string;
  pillText?: string;
}

export const InfoWithImage: React.FC<InfoWithImageProps> = ({
  title,
  description,
  imageUrl,
  pillText,
}) => {
  return (
    <View>
      <ImageBackground
        source={{
          uri: imageUrl,
        }}
        style={styles.image}
      />
      <Text style={styles.title}>{title}</Text>
      {pillText && <Pill label={pillText} />}
      <Text style={styles.description}>{description}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    width: "100%",
    height: 300,
    borderRadius: theme.radii.lg,
    overflow: "hidden",
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: theme.fontSizes.lg,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    fontWeight: "800",
    color: theme.colors.primary,
  },
  description: {
    marginTop: theme.spacing.md,
    fontSize: theme.fontSizes.md,
    color: theme.colors.black,
  },
});
