import React from "react";
import { TextInput, Text, View, StyleSheet } from "react-native";
import { Controller, FieldValues, Path, Control } from "react-hook-form";

interface TextAreaFormFieldProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  placeholder?: string;
  numberOfLines?: number;
}

export const TextAreaFormField = <T extends FieldValues>({
  control,
  name,
  placeholder,
  numberOfLines = 4,
}: TextAreaFormFieldProps<T>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({
        field: { onChange, onBlur, value },
        fieldState: { error },
      }) => (
        <View style={styles.container}>
          <TextInput
            style={[styles.textArea, error && styles.errorBorder]}
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            placeholder={placeholder}
            multiline
            numberOfLines={numberOfLines}
            textAlignVertical="top"
            autoCapitalize="none"
          />
          {error?.message && (
            <Text style={styles.errorText}>{error.message}</Text>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  textArea: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    lineHeight: 22,
    minHeight: 100,
  },
  errorBorder: {
    borderColor: "#E5484D",
  },
  errorText: {
    marginTop: 4,
    color: "#E5484D",
    fontSize: 12,
  },
});
