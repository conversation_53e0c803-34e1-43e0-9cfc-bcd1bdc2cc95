import React, { useState } from "react";
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInputProps,
} from "react-native";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons"; // Usa otro set si lo prefieres

interface FormFieldProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: TextInputProps["keyboardType"];
  autoCapitalize?: TextInputProps["autoCapitalize"];
  icon?: React.ReactNode;
}

export const FormField = <T extends FieldValues>({
  name,
  control,
  label,
  placeholder,
  secureTextEntry,
  keyboardType,
  autoCapitalize = "none",
  icon,
}: FormFieldProps<T>) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { value, onChange, onBlur },
        fieldState: { error },
      }) => (
        <View style={styles.container}>
          {label && <Text style={styles.label}>{label}</Text>}

          <View
            style={[
              styles.inputContainer,
              !!error && styles.inputContainerError,
            ]}
          >
            {icon && <View style={styles.iconContainer}>{icon}</View>}

            <TextInput
              style={styles.input}
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              placeholder={placeholder}
              secureTextEntry={secureTextEntry && !showPassword}
              keyboardType={keyboardType}
              autoCapitalize={autoCapitalize}
            />

            {secureTextEntry && (
              <TouchableOpacity
                onPress={() => setShowPassword((prev) => !prev)}
              >
                <Ionicons
                  name={showPassword ? "eye-off" : "eye"}
                  size={20}
                  color="gray"
                />
              </TouchableOpacity>
            )}
          </View>

          {error?.message && (
            <Text style={styles.errorText}>{error.message}</Text>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: "100%",
  },
  label: {
    fontWeight: "600",
    marginBottom: 6,
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: "#F0F0F0",
  },
  inputContainerError: {
    borderColor: "#E5484D",
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
  },
  iconContainer: {
    marginRight: 8,
  },
  errorText: {
    color: "#E5484D",
    fontSize: 12,
    marginTop: 4,
  },
});
