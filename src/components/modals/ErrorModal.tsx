import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import Modal from "react-native-modal";
import { Ionicons } from "@expo/vector-icons";
import { theme } from "../../theme";

interface ErrorModalProps {
  visible: boolean;
  title: string;
  message: string;
  onClose: () => void;
  buttonText?: string;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({
  visible,
  title,
  message,
  onClose,
  buttonText = "Intentar de nuevo",
}) => {
  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      style={styles.modal}
      animationIn="shake"
      animationOut="zoomOut"
    >
      <View style={styles.container}>
        <View style={styles.iconContainer}>
          <Ionicons
            name="close-circle"
            size={theme.fontSizes.xxxl}
            color={theme.colors.error}
          />
        </View>

        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>

        <TouchableOpacity style={styles.button} onPress={onClose}>
          <Text style={styles.buttonText}>{buttonText}</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "center",
    margin: theme.spacing.lg,
  },
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.radii.xl,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "800",
    color: theme.colors.error,
    textAlign: "center",
    marginBottom: theme.spacing.md,
  },
  message: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray700,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: theme.spacing.xl,
  },
  button: {
    backgroundColor: theme.colors.error,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.radii.xl,
    minWidth: 120,
  },
  buttonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
    textAlign: "center",
  },
});
