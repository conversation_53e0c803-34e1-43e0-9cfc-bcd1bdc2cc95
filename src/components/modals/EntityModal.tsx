import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import Mo<PERSON> from "react-native-modal";
import { theme } from "../../theme";

interface EntityModalProps {
  visible: boolean;
  isEditMode: boolean;
  title: string;
  onConfirm: () => void;
  onCancel: () => void;
  children: React.ReactNode;
}

export const EntityModal: React.FC<EntityModalProps> = ({
  visible,
  isEditMode,
  title,
  onConfirm,
  onCancel,
  children,
}) => {
  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onCancel}
      onBackButtonPress={onCancel}
      style={styles.modal}
      swipeDirection="down"
      onSwipeComplete={onCancel}
      propagateSwipe
    >
      <View style={styles.bottomSheet}>
        <Text style={styles.title}>
          {isEditMode ? `Editar ${title}` : `<PERSON>rear ${title}`}
        </Text>
        <View style={styles.body}>{children}</View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end", // 👈 importante
    margin: 0,
  },
  bottomSheet: {
    backgroundColor: theme.colors.white,
    borderTopLeftRadius: theme.radii.lg,
    borderTopRightRadius: theme.radii.lg,
    padding: theme.spacing.lg,
    height: "80%", // 👈 controla el tamaño
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "800",
    marginBottom: theme.spacing.md,
    color: theme.colors.gold,
  },
  body: {
    flex: 1,
  },
});
