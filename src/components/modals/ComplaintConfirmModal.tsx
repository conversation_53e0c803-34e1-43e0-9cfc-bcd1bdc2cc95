import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import Modal from "react-native-modal";
import { Ionicons } from "@expo/vector-icons";
import { theme } from "../../theme";

interface ComplaintData {
  type: string;
  detail: string;
  priority?: string;
  imagesCount: number;
}

interface ComplaintConfirmModalProps {
  visible: boolean;
  complaintData: ComplaintData | null;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  title?: string;
}

export const ComplaintConfirmModal: React.FC<ComplaintConfirmModalProps> = ({
  visible,
  complaintData,
  onConfirm,
  onCancel,
  isLoading = false,
  title = "Confirmar Envío",
}) => {
  if (!complaintData) return null;

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={!isLoading ? onCancel : undefined}
      onBackButtonPress={!isLoading ? onCancel : undefined}
      style={styles.modal}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Ionicons
            name="document-text-outline"
            size={theme.fontSizes.xl}
            color={theme.colors.primary}
          />
          <Text style={styles.title}>{title}</Text>
        </View>

        <View style={styles.content}>
          <View style={styles.infoRow}>
            <Ionicons
              name="flag-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Tipo:</Text>
            <Text style={styles.value}>{complaintData.type}</Text>
          </View>

          {complaintData.priority && (
            <View style={styles.infoRow}>
              <Ionicons
                name="alert-circle-outline"
                size={theme.fontSizes.md}
                color={theme.colors.gray700}
              />
              <Text style={styles.label}>Prioridad:</Text>
              <Text style={styles.value}>{complaintData.priority}</Text>
            </View>
          )}

          <View style={styles.infoRow}>
            <Ionicons
              name="document-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Descripción:</Text>
            <Text style={[styles.value, styles.description]} numberOfLines={3}>
              {complaintData.detail}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Ionicons
              name="images-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
            <Text style={styles.label}>Imágenes:</Text>
            <Text style={styles.value}>
              {complaintData.imagesCount} {complaintData.imagesCount === 1 ? "imagen" : "imágenes"}
            </Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onCancel}
            disabled={isLoading}
          >
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.confirmButton, isLoading && styles.disabledButton]}
            onPress={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Ionicons
                  name="hourglass-outline"
                  size={theme.fontSizes.sm}
                  color={theme.colors.white}
                />
                <Text style={styles.confirmButtonText}>Enviando...</Text>
              </View>
            ) : (
              <Text style={styles.confirmButtonText}>Confirmar Envío</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "center",
    margin: theme.spacing.lg,
  },
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.radii.xl,
    padding: theme.spacing.lg,
  },
  header: {
    alignItems: "center",
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "800",
    color: theme.colors.primary,
    marginTop: theme.spacing.sm,
    textAlign: "center",
  },
  content: {
    marginBottom: theme.spacing.lg,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  },
  label: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.gray700,
    marginLeft: theme.spacing.sm,
    minWidth: 80,
  },
  value: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray900,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  description: {
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: theme.spacing.sm,
  },
  button: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.radii.xl,
    alignItems: "center",
    justifyContent: "center",
  },
  cancelButton: {
    backgroundColor: theme.colors.gray200,
  },
  cancelButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.gray700,
  },
  confirmButton: {
    backgroundColor: theme.colors.primary,
  },
  confirmButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  disabledButton: {
    backgroundColor: theme.colors.gray300,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: theme.spacing.xs,
  },
});
