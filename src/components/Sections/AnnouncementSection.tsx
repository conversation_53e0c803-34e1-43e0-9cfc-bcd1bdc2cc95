import { Card, Row, Section } from "../main";
import {
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
} from "react-native";
import { Col } from "../main/Col";
import { theme } from "../../theme";
import { PartialAnnouncement } from "../../interfaces/me";
import { getDateFromJSDate } from "../../utils/date-time.utils";

interface AnnouncementSection {
  announcements: PartialAnnouncement[];
  onAnnouncementPress: (announcement: PartialAnnouncement) => void;
}

export const AnnouncementSection: React.FC<AnnouncementSection> = ({
  announcements,
  onAnnouncementPress,
}) => {
  return (
    <Section title={"Comunicados"}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
      >
        {announcements.length ? (
          announcements.map((announcement) => (
            <TouchableOpacity
              key={announcement.id}
              onPress={() => onAnnouncementPress(announcement)}
            >
              <Card key={announcement.id} style={styles.card}>
                <ImageBackground
                  source={{
                    uri: announcement.images[0].path ?? "",
                  }}
                  style={styles.imageBackground}
                  imageStyle={{ borderRadius: theme.radii.md }}
                >
                  <Col style={styles.mainContainer}>
                    <Text style={styles.title}>{announcement.title}</Text>
                    <Row justify="space-between" style={styles.textContainer}>
                      <Text style={styles.date}>
                        {getDateFromJSDate(announcement.createdAt)}
                      </Text>
                    </Row>
                  </Col>
                </ImageBackground>
              </Card>
            </TouchableOpacity>
          ))
        ) : (
          <Text>No hay comunicados</Text>
        )}
      </ScrollView>
    </Section>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: theme.spacing.xs,
    width: 250,
    height: 200,
    padding: 0,
    marginRight: theme.spacing.md,
  },
  mainContainer: {
    padding: theme.spacing.md,
    flex: 1,
    justifyContent: "flex-end",
  },
  imageBackground: {
    flex: 1,
    justifyContent: "center",
  },
  textContainer: {
    marginTop: theme.spacing.xs,
  },
  title: {
    color: theme.colors.gray300,
    fontWeight: "700",
    fontSize: theme.fontSizes.lg,
  },
  date: {
    backgroundColor: theme.colors.primaryDark,
    borderRadius: theme.radii.pill,
    paddingHorizontal: theme.spacing.sm,
    color: theme.colors.gold,
  },
  readText: {
    color: theme.colors.white,
    fontWeight: "600",
  },
});
