import { ParkingSpot } from "../../interfaces/parking-spot";
import { theme } from "../../theme";
import { Card, Col, Row } from "../main";
import { Section } from "../main/Section";
import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { colors } from "../../theme/theme";

interface ParkingSpotsSectionProps {
  parkingSpots: ParkingSpot[];
}

export const ParkingSpotsSection: React.FC<ParkingSpotsSectionProps> = ({
  parkingSpots,
}) => {
  return (
    <Section title="Lugares de estacionamiento">
      <Col>
        {parkingSpots.map((parkingSpot) => (
          <Text
            key={parkingSpot.id}
            style={{
              color: parkingSpot.isAvailable
                ? theme.colors.primary
                : theme.colors.secondaryDark,
              fontWeight: 700,
            }}
          >
            {parkingSpot.spotNumber}
          </Text>
        ))}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: theme.spacing.xs,
    width: 150,
    height: 60,
  },
  available: {
    borderWidth: 3,
    borderColor: theme.colors.success,
  },
  occupied: {
    borderWidth: 3,
    borderColor: theme.colors.warning,
  },
});
