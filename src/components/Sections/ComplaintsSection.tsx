import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { Complaint } from "../../interfaces/complaint";
import { Card, Row, Section } from "../main";
import { getProirity, getStatus } from "../../utils/convertions";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";

interface ComplaintsSectionProps {
  complaints: Complaint[];
}

export const ComplaintsSection: React.FC<ComplaintsSectionProps> = ({
  complaints,
}) => {
  return (
    <Section title={"Quejas abiertas"} create>
      <>
        {complaints.length ? (
          complaints.map((complaint) => (
            <TouchableOpacity
              key={complaint.id}
              onPress={() => console.log("Pressed")}
            >
              <Card style={styles.card}>
                <Row>
                  <Row align="center">
                    <MaterialCommunityIcons
                      name="flag-outline"
                      size={theme.fontSizes.lg}
                      color={theme.colors.primary}
                    />
                    <Text style={styles.text}>
                      {getProirity(complaint.priority)}
                    </Text>
                  </Row>
                  <Row align="center" style={styles.row}>
                    <MaterialCommunityIcons
                      name="information-outline"
                      size={theme.fontSizes.lg}
                      color={theme.colors.primary}
                    />
                    <Text> {getStatus(complaint.status)}</Text>
                  </Row>
                </Row>
                <Row align="center">
                  <MaterialCommunityIcons
                    name="card-text"
                    size={theme.fontSizes.lg}
                    color={theme.colors.primary}
                  />
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    style={styles.text}
                  >
                    {complaint.detail}
                  </Text>
                </Row>
              </Card>
            </TouchableOpacity>
          ))
        ) : (
          <Text>No hay quejas abiertas</Text>
        )}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  row: {
    marginLeft: theme.spacing.sm,
    marginRight: theme.spacing.sm,
  },
  text: {
    paddingLeft: theme.spacing.sm,
  },
  card: {
    flexDirection: "column",
    justifyContent: "space-between",
  },
});
