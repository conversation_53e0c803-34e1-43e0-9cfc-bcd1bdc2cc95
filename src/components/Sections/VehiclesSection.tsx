// src/components/property/VehicleList.tsx

import { StyleSheet, Text } from "react-native";
import { Vehicle } from "../../interfaces/vehicle";
import { Col, Row, Section } from "../main";
import { theme } from "../../theme/theme";

interface VehiclesSectionProps {
  vehicles: Vehicle[];
}

export const VehiclesSection: React.FC<VehiclesSectionProps> = ({
  vehicles,
}) => {
  if (!vehicles.length) {
    return <Text style={styles.message}>No hay vehículos registrados.</Text>;
  }

  return (
    <Section title="Vehículos">
      <Col>
        {vehicles.map((vehicle) => (
          <Row key={vehicle.id} align="center">
            <Text style={styles.model}>
              {vehicle.brand} {vehicle.model}
            </Text>
            <Text style={styles.color}>{vehicle.color}</Text>
            <Text style={styles.plate}>{vehicle.plate}</Text>
          </Row>
        ))}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.md,
  },
  message: {
    padding: theme.fontSizes.md,
    textAlign: "center",
    color: "#555",
  },
  model: {
    fontSize: theme.fontSizes.sm,
    // fontWeight: "600",
    paddingRight: theme.spacing.sm,
  },
  color: {
    fontSize: theme.fontSizes.sm,
    // color: "#444",
  },
  plate: {
    borderWidth: 2,
    borderRadius: theme.radii.sm,
    borderColor: theme.colors.primaryDark,
    padding: theme.spacing.xs,
    marginLeft: theme.spacing.sm,
  },
});
