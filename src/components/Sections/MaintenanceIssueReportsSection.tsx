import { Text, TouchableOpacity } from "react-native";
import { Card, Col, Section } from "../main";
import { PartialMaintenanceIssueReport } from "../../interfaces/me";

interface MaintenanceIssueReportsSectionProps {
  maintenanceIssueReports: PartialMaintenanceIssueReport[];
}

export const MaintenanceIssueReportsSection: React.FC<
  MaintenanceIssueReportsSectionProps
> = ({ maintenanceIssueReports }) => {
  const sectionTitle = "Reportes de mantenimiento";

  return (
    <Section title={sectionTitle} create>
      <Col>
        {maintenanceIssueReports.map((mainteanceIssueReport) => (
          <TouchableOpacity
            key={mainteanceIssueReport.id}
            onPress={() => console.log("Pressed")}
          >
            <Card>
              <Text>{mainteanceIssueReport.status}</Text>
              <Text>{mainteanceIssueReport.createdAt}</Text>
              <Text>{mainteanceIssueReport.description}</Text>
            </Card>
          </TouchableOpacity>
        ))}
      </Col>
    </Section>
  );
};
