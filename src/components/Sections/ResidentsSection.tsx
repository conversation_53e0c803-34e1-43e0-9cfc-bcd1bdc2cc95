import { StyleSheet, Text } from "react-native";
import { Section } from "../main/Section";
import { Col, Row } from "../main";
import { SmartAvatar } from "../main/Avatar/SmartAvatar";
import { theme } from "../../theme";
import { PartialResident } from "../../interfaces/me";

interface ResidentsSectionProps {
  residents: PartialResident[];
}

export const ResidentsSection: React.FC<ResidentsSectionProps> = ({
  residents,
}) => {
  return (
    <Section title="Residentes">
      <Col>
        {residents.map((resident) => {
          const name = `${resident.firstName} ${resident.paternalLastName}`;
          return (
            <Row align="center" key={resident.id} style={styles.container}>
              <SmartAvatar name={name} />
              <Col style={styles.textContainer}>
                <Text>
                  {name} {resident.maternalLastName}
                </Text>
                <Row>
                  {resident.roles.map((role) => (
                    <Text key={role.id} style={styles.role}>
                      {`${role.name} `}
                    </Text>
                  ))}
                </Row>
              </Col>
            </Row>
          );
        })}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.sm,
  },
  textContainer: {
    paddingLeft: theme.spacing.sm,
  },
  role: {
    color: theme.colors.gray500,
  },
});
