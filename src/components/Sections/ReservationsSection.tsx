import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { PartialReservation } from "../../interfaces/me";
import { Card, Row, Section } from "../main";
import { formatDateDMY } from "../../utils/date-time.utils";
import { getFacilityIcons } from "../../utils/icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme/theme";
import { getReservationStatus } from "../../utils/convertions";

interface ReservationsSectionProps {
  reservations: PartialReservation[];
}

export const ReservationsSection: React.FC<ReservationsSectionProps> = ({
  reservations,
}) => {
  return (
    <Section title="Reservaciones próximas">
      <>
        {reservations?.length ? (
          reservations.map((reservation) => {
            return (
              <TouchableOpacity key={reservation.id}>
                <Card>
                  <Row>
                    <Row align="center" style={styles.row}>
                      {getFacilityIcons(reservation.facility.name)}
                      <Text style={styles.text}>
                        {getReservationStatus(reservation.status)}
                      </Text>
                    </Row>
                    <Row align="center" style={styles.row}>
                      <MaterialCommunityIcons
                        name="calendar-month"
                        size={theme.fontSizes.lg}
                        color={theme.colors.primary}
                      />
                      <Text style={styles.text}>
                        {formatDateDMY(reservation.startDateTime)}
                      </Text>
                    </Row>
                    <Row align="center" style={styles.row}>
                      <MaterialCommunityIcons
                        name="account-multiple"
                        size={theme.fontSizes.lg}
                        color={theme.colors.primary}
                      />
                      <Text style={styles.text}>
                        {reservation.amountOfPeople}
                      </Text>
                    </Row>
                  </Row>
                </Card>
              </TouchableOpacity>
            );
          })
        ) : (
          <Text>No hay reservaciones próximas</Text>
        )}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  row: {
    marginLeft: theme.spacing.sm,
    marginRight: theme.spacing.sm,
  },
  text: {
    paddingLeft: theme.spacing.sm,
  },
});
