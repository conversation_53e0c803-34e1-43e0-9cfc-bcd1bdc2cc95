import { LinearGradient } from "expo-linear-gradient";
import { Col, Row, Title } from "../main";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { theme } from "../../theme";
import { ReactNode } from "react";
import { ScrollView } from "react-native-gesture-handler";

interface GradientViewProps {
  firstLineText: string;
  children: React.ReactNode;
  secondLineText?: string;
  icon?: ReactNode;
  action?: () => void;
}

export const GradientView: React.FC<GradientViewProps> = ({
  firstLineText,
  secondLineText,
  icon,
  children,
  action,
}) => {
  return (
    <LinearGradient
      colors={["#1B4959", "#DFD6C6"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 2.5, y: 0 }}
      style={{ flex: 1 }}
    >
      <Row style={styles.header}>
        <Col>
          <Title size="xl" style={styles.headerText}>
            {firstLineText}
          </Title>
          {secondLineText && (
            <Title size="xl" style={styles.headerText}>
              {secondLineText}
            </Title>
          )}
        </Col>
        {icon && action && (
          <TouchableOpacity onPress={action}>{icon}</TouchableOpacity>
        )}
      </Row>
      <View style={styles.body}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <Col style={styles.content}>{children}</Col>
        </ScrollView>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 20,
    paddingBottom: "10%",
  },
  headerText: {
    color: theme.colors.white,
  },
  body: {
    flex: 1, // ocupa el resto del espacio debajo del header
    backgroundColor: "white",
    borderRadius: theme.radii.xl,
    // overflow: "hidden", // opcional para que los bordes redondeados se respeten
    padding: theme.spacing.lg,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 5,
    marginBottom: 30,
  },
  scrollContent: {
    paddingBottom: theme.spacing.xl,
  },
  content: {
    flexGrow: 1,
  },
});
