import React from "react";
import {
  View,
  ViewProps,
  StyleSheet,
  ViewStyle,
  StyleProp,
} from "react-native";

interface ColProps extends ViewProps {
  gap?: number;
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
  align?: ViewStyle["alignItems"];
  justify?: ViewStyle["justifyContent"];
}

export const Col: React.FC<ColProps> = ({
  gap,
  style,
  children,
  align,
  justify,
  ...rest
}) => (
  <View
    style={[
      styles.col,
      { rowGap: gap ?? 0, alignItems: align, justifyContent: justify },
      style,
    ]}
    {...rest}
  >
    {children}
  </View>
);

const styles = StyleSheet.create({
  col: {
    flexDirection: "column",
    flex: 1,
  },
});
