import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
import { getStringTime } from "../../utils/date-time.utils";
import { Row } from "./Row";
import { Ionicons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Col } from "./Col";
import { Button } from "./buttons/Button";

export interface SimpleTimePickerProps {
  maxTimeOfStay: number;
  start: string; // ej. "09:00"
  end: string; // ej. "21:00"
  onChange?: (value: {
    startTime: string | null;
    endTime: string | null;
  }) => void;
}

export const SimpleTimePicker: React.FC<SimpleTimePickerProps> = ({
  start,
  end,
  maxTimeOfStay,
  onChange,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [startTime, setStartTime] = useState<string | null>(null);
  const [endTime, setEndTime] = useState<string | null>(null);

  const openTimeForArray = Number(getStringTime(start).split(":")[0]);
  const closeTimeForArray = Number(getStringTime(end).split(":")[0]);

  // Todas las horas posibles
  const hours = Array.from(
    { length: closeTimeForArray - openTimeForArray + 1 },
    (_, i) => i + openTimeForArray
  );

  // Utilidad para convertir "HH:MM" a minutos totales
  const toMinutes = (time: string) => {
    const [h, m] = time.split(":").map(Number);
    return h * 60 + m;
  };

  // Rango seleccionado: [startTime, endTime)
  const isInRange = (label: string) => {
    if (startTime && endTime) {
      return (
        toMinutes(label) >= toMinutes(startTime) &&
        toMinutes(label) < toMinutes(endTime)
      );
    }
    return false;
  };

  // Determinar si todo el rango posible está seleccionado
  let allPossibleSelected = false;
  if (startTime && endTime) {
    const spaces = (toMinutes(endTime) - toMinutes(startTime)) / 60;
    allPossibleSelected = spaces === maxTimeOfStay;
  }

  // Lógica de selección
  const handleOptionPress = (label: string) => {
    // Si nada está seleccionado, selecciona como inicio
    if (allPossibleSelected) {
      setStartTime(null);
      setEndTime(null);
      return;
    }
    if (!startTime && !endTime) {
      setStartTime(label);
      setEndTime(null);
      return;
    }

    // Si solo inicio seleccionado
    if (startTime && !endTime) {
      if (toMinutes(label) < toMinutes(startTime)) {
        // Cambia el inicio
        setStartTime(label);
        setEndTime(null);
      } else if (toMinutes(label) === toMinutes(startTime)) {
        // Deselecciona todo
        setStartTime(null);
        setEndTime(null);
      } else {
        // Selecciona como fin
        const diff = (toMinutes(label) - toMinutes(startTime)) / 60;
        if (diff > maxTimeOfStay || diff < 1) {
          Alert.alert(
            "Rango inválido",
            `Selecciona al menos 1 hora y máximo ${maxTimeOfStay} horas.`
          );
          return;
        }
        setEndTime(label);
      }
      return;
    }

    // Si ambos están seleccionados
    if (startTime && endTime) {
      if (toMinutes(label) === toMinutes(startTime)) {
        // Reinicia la selección
        setStartTime(null);
        setEndTime(null);
        return;
      }
      // Si tocamos antes del inicio, es nuevo inicio
      if (toMinutes(label) < toMinutes(startTime)) {
        setStartTime(label);
        setEndTime(null);
      }
      // Si tocamos después del inicio, actualiza el fin si es válido
      else if (toMinutes(label) > toMinutes(startTime)) {
        const diff = (toMinutes(label) - toMinutes(startTime)) / 60;
        if (diff > maxTimeOfStay) {
          setStartTime(null);
          setEndTime(null);
          return;
        }
        if (diff < 1) {
          Alert.alert(
            "Rango inválido",
            `Selecciona al menos 1 hora y máximo ${maxTimeOfStay} horas.`
          );
          return;
        }
        setEndTime(label);
      }
    }
  };

  // Confirmar selección
  const handleConfirm = () => {
    if (!startTime || !endTime) {
      Alert.alert("Selecciona inicio y fin");
      return;
    }
    setShowModal(false);
    if (onChange) onChange({ startTime, endTime });
  };

  // Limpiar selección al cerrar modal
  const handleClose = () => {
    setShowModal(false);
    setStartTime(null);
    setEndTime(null);
  };

  return (
    <Col style={{ paddingTop: theme.spacing.md }}>
      <TouchableOpacity onPress={() => setShowModal(true)}>
        <Row align="center">
          <Ionicons
            name="time-outline"
            size={theme.fontSizes.md}
            color={theme.colors.gray700}
          />
          <Text style={styles.text}>
            {startTime && endTime
              ? `${startTime} - ${endTime}`
              : "Seleccionar horario"}
          </Text>
        </Row>
      </TouchableOpacity>

      <Modal visible={showModal} transparent animationType="fade">
        <View style={styles.overlay}>
          <View style={styles.modal}>
            <Text style={styles.title}>Selecciona inicio y fin</Text>
            <View style={styles.row}>
              {hours.map((h) => {
                const label = `${h.toString().padStart(2, "0")}:00`;
                const selectedAsStart = startTime === label;
                const selectedAsEnd = endTime === label;
                const between = isInRange(label);

                // Lógica de deshabilitado
                let disabled = false;
                if (allPossibleSelected && startTime && endTime) {
                  if (
                    toMinutes(label) < toMinutes(startTime) ||
                    toMinutes(label) > toMinutes(endTime)
                  ) {
                    disabled = true;
                  }
                } else if (startTime && !endTime) {
                  // Deshabilita antes del inicio
                  if (toMinutes(label) < toMinutes(startTime)) {
                    disabled = true;
                  }
                  // Solo permite seleccionar fin hasta maxTimeOfStay
                  const diff = (toMinutes(label) - toMinutes(startTime)) / 60;
                  if (
                    toMinutes(label) > toMinutes(startTime) &&
                    (diff > maxTimeOfStay || diff < 1)
                  ) {
                    disabled = true;
                  }
                }
                return (
                  <TouchableOpacity
                    key={label}
                    style={[
                      styles.option,
                      selectedAsStart && styles.selectedStart,
                      selectedAsEnd && styles.selectedEnd,
                      between && styles.inRange,
                      disabled && styles.disabledOption,
                    ]}
                    disabled={disabled}
                    onPress={() => handleOptionPress(label)}
                  >
                    <Text
                      style={{
                        fontSize: 16,
                        color:
                          selectedAsStart || selectedAsEnd || between
                            ? "#fff"
                            : disabled
                            ? "#bbb"
                            : "#000",
                        fontWeight:
                          selectedAsStart || selectedAsEnd ? "bold" : "normal",
                      }}
                    >
                      {label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
            <View style={{ width: "100%" }}>
              <Button
                title="Confirmar horario"
                onPress={handleConfirm}
                disabled={!startTime || !endTime}
                style={{
                  marginVertical: theme.spacing.sm,
                  backgroundColor:
                    startTime && endTime ? theme.colors.primary : "#ccc",
                }}
              />
              <Button
                title="Cancelar"
                onPress={handleClose}
                style={{ backgroundColor: "#ccc" }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </Col>
  );
};

const styles = StyleSheet.create({
  text: {
    marginLeft: theme.spacing.sm,
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.xl,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  modal: {
    backgroundColor: "white",
    borderRadius: theme.radii.xl,
    padding: 16,
    width: "90%",
    maxHeight: "80%",
    alignItems: "center",
  },
  title: {
    fontWeight: "bold",
    fontSize: 18,
    marginBottom: 12,
  },
  row: {
    flexDirection: "row",
    flexWrap: "wrap",
    maxHeight: 240,
    marginBottom: 12,
    justifyContent: "center",
  },
  option: {
    padding: 8,
    borderBottomWidth: 0.5,
    borderColor: "#eee",
    alignItems: "center",
    minWidth: 70,
    margin: 2,
    borderRadius: 6,
    backgroundColor: "#fff",
  },
  selectedStart: {
    backgroundColor: theme.colors.primary,
  },
  selectedEnd: {
    backgroundColor: theme.colors.primaryDark,
  },
  inRange: {
    backgroundColor: theme.colors.primaryDark,
  },
  disabledOption: {
    backgroundColor: theme.colors.primaryLight,
  },
});
