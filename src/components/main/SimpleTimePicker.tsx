import React, { useState, useMemo, useCallback } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
import { getStringTime } from "../../utils/date-time.utils";
import { Row } from "./Row";
import { Ionicons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Col } from "./Col";
import { Button } from "./buttons/Button";

export interface SimpleTimePickerProps {
  maxTimeOfStay: number;
  start: string; // ej. "09:00"
  end: string; // ej. "21:00"
  onChange?: (value: {
    startTime: string | null;
    endTime: string | null;
  }) => void;
}

// Utilidades extraídas para reducir complejidad
const toMinutes = (time: string): number => {
  const [h, m] = time.split(":").map(Number);
  return h * 60 + m;
};

const calculateTimeDifference = (start: string, end: string): number => {
  return (toMinutes(end) - toMinutes(start)) / 60;
};

const isValidTimeRange = (
  start: string,
  end: string,
  maxHours: number
): boolean => {
  const diff = calculateTimeDifference(start, end);
  return diff >= 1 && diff <= maxHours;
};

const showInvalidRangeAlert = (maxTimeOfStay: number): void => {
  Alert.alert(
    "Rango inválido",
    `Selecciona al menos 1 hora y máximo ${maxTimeOfStay} horas.`
  );
};

// Función para determinar el color del texto
const getTextColor = (isSelected: boolean, isDisabled: boolean): string => {
  if (isSelected) return "#fff";
  if (isDisabled) return "#bbb";
  return "#000";
};

// Función para determinar si una opción está deshabilitada
const isOptionDisabled = (
  label: string,
  startTime: string | null,
  endTime: string | null,
  allPossibleSelected: boolean,
  maxTimeOfStay: number
): boolean => {
  if (allPossibleSelected && startTime && endTime) {
    return (
      toMinutes(label) < toMinutes(startTime) ||
      toMinutes(label) > toMinutes(endTime)
    );
  }

  if (startTime && !endTime) {
    if (toMinutes(label) < toMinutes(startTime)) {
      return true;
    }

    const diff = calculateTimeDifference(startTime, label);
    return (
      toMinutes(label) > toMinutes(startTime) &&
      (diff > maxTimeOfStay || diff < 1)
    );
  }

  return false;
};

// Componente para renderizar cada opción de tiempo
interface TimeOptionProps {
  label: string;
  startTime: string | null;
  endTime: string | null;
  allPossibleSelected: boolean;
  maxTimeOfStay: number;
  isInRange: (label: string) => boolean;
  onPress: (label: string) => void;
}

const TimeOption: React.FC<TimeOptionProps> = ({
  label,
  startTime,
  endTime,
  allPossibleSelected,
  maxTimeOfStay,
  isInRange,
  onPress,
}) => {
  const selectedAsStart = startTime === label;
  const selectedAsEnd = endTime === label;
  const between = isInRange(label);
  const disabled = isOptionDisabled(
    label,
    startTime,
    endTime,
    allPossibleSelected,
    maxTimeOfStay
  );

  const isSelected = selectedAsStart || selectedAsEnd || between;
  const textColor = getTextColor(isSelected, disabled);
  const fontWeight = selectedAsStart || selectedAsEnd ? "bold" : "normal";

  return (
    <TouchableOpacity
      key={label}
      style={[
        styles.option,
        selectedAsStart && styles.selectedStart,
        selectedAsEnd && styles.selectedEnd,
        between && styles.inRange,
        disabled && styles.disabledOption,
      ]}
      disabled={disabled}
      onPress={() => onPress(label)}
    >
      <Text
        style={{
          fontSize: 16,
          color: textColor,
          fontWeight,
        }}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

export const SimpleTimePicker: React.FC<SimpleTimePickerProps> = ({
  start,
  end,
  maxTimeOfStay,
  onChange,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [startTime, setStartTime] = useState<string | null>(null);
  const [endTime, setEndTime] = useState<string | null>(null);

  // Memoizar valores calculados
  const { hours, allPossibleSelected } = useMemo(() => {
    const openTimeForArray = Number(getStringTime(start).split(":")[0]);
    const closeTimeForArray = Number(getStringTime(end).split(":")[0]);

    const hoursArray = Array.from(
      { length: closeTimeForArray - openTimeForArray + 1 },
      (_, i) => i + openTimeForArray
    );

    let allSelected = false;
    if (startTime && endTime) {
      const spaces = calculateTimeDifference(startTime, endTime);
      allSelected = spaces === maxTimeOfStay;
    }

    return {
      hours: hoursArray,
      allPossibleSelected: allSelected,
    };
  }, [start, end, startTime, endTime, maxTimeOfStay]);

  // Rango seleccionado: [startTime, endTime)
  const isInRange = useCallback(
    (label: string) => {
      if (startTime && endTime) {
        return (
          toMinutes(label) >= toMinutes(startTime) &&
          toMinutes(label) < toMinutes(endTime)
        );
      }
      return false;
    },
    [startTime, endTime]
  );

  // Funciones auxiliares para manejar la selección
  const handleNoSelection = useCallback((label: string) => {
    setStartTime(label);
    setEndTime(null);
  }, []);

  const handleStartOnlySelection = useCallback(
    (label: string) => {
      if (!startTime) return;

      const labelMinutes = toMinutes(label);
      const startMinutes = toMinutes(startTime);

      if (labelMinutes < startMinutes) {
        setStartTime(label);
        setEndTime(null);
      } else if (labelMinutes === startMinutes) {
        setStartTime(null);
        setEndTime(null);
      } else {
        if (!isValidTimeRange(startTime, label, maxTimeOfStay)) {
          showInvalidRangeAlert(maxTimeOfStay);
          return;
        }
        setEndTime(label);
      }
    },
    [startTime, maxTimeOfStay]
  );

  const handleBothSelectedCase = useCallback(
    (label: string) => {
      if (!startTime || !endTime) return;

      const labelMinutes = toMinutes(label);
      const startMinutes = toMinutes(startTime);

      if (labelMinutes === startMinutes) {
        setStartTime(null);
        setEndTime(null);
        return;
      }

      if (labelMinutes < startMinutes) {
        setStartTime(label);
        setEndTime(null);
      } else if (labelMinutes > startMinutes) {
        const diff = calculateTimeDifference(startTime, label);
        if (diff > maxTimeOfStay) {
          setStartTime(null);
          setEndTime(null);
          return;
        }
        if (diff < 1) {
          showInvalidRangeAlert(maxTimeOfStay);
          return;
        }
        setEndTime(label);
      }
    },
    [startTime, endTime, maxTimeOfStay]
  );

  // Lógica de selección simplificada
  const handleOptionPress = useCallback(
    (label: string) => {
      if (!startTime && !endTime) {
        handleNoSelection(label);
      } else if (startTime && !endTime) {
        handleStartOnlySelection(label);
      } else if (startTime && endTime) {
        handleBothSelectedCase(label);
      }
    },
    [
      startTime,
      endTime,
      handleNoSelection,
      handleStartOnlySelection,
      handleBothSelectedCase,
    ]
  );

  // Confirmar selección
  const handleConfirm = useCallback(() => {
    if (!startTime || !endTime) {
      Alert.alert("Selecciona inicio y fin");
      return;
    }
    setShowModal(false);
    if (onChange) onChange({ startTime, endTime });
  }, [startTime, endTime, onChange]);

  // Limpiar selección al cerrar modal
  const handleClose = useCallback(() => {
    setShowModal(false);
    setStartTime(null);
    setEndTime(null);
  }, []);

  // Abrir modal
  const handleOpenModal = useCallback(() => {
    setShowModal(true);
  }, []);

  // Memoizar el texto mostrado
  const displayText = useMemo(() => {
    return startTime && endTime
      ? `${startTime} - ${endTime}`
      : "Seleccionar horario";
  }, [startTime, endTime]);

  return (
    <Col style={{ paddingTop: theme.spacing.md }}>
      <TouchableOpacity onPress={handleOpenModal}>
        <Row align="center">
          <Ionicons
            name="time-outline"
            size={theme.fontSizes.md}
            color={theme.colors.gray700}
          />
          <Text style={styles.text}>{displayText}</Text>
        </Row>
      </TouchableOpacity>

      <Modal visible={showModal} transparent animationType="fade">
        <View style={styles.overlay}>
          <View style={styles.modal}>
            <Text style={styles.title}>Selecciona inicio y fin</Text>
            <View style={styles.row}>
              {hours.map((h) => {
                const label = `${h.toString().padStart(2, "0")}:00`;
                return (
                  <TimeOption
                    key={label}
                    label={label}
                    startTime={startTime}
                    endTime={endTime}
                    allPossibleSelected={allPossibleSelected}
                    maxTimeOfStay={maxTimeOfStay}
                    isInRange={isInRange}
                    onPress={handleOptionPress}
                  />
                );
              })}
            </View>
            <View style={{ width: "100%" }}>
              <Button
                title="Confirmar horario"
                onPress={handleConfirm}
                disabled={!startTime || !endTime}
                style={[
                  styles.confirmButton,
                  {
                    backgroundColor:
                      startTime && endTime ? theme.colors.primary : "#ccc",
                  },
                ]}
              />
              <Button
                title="Cancelar"
                onPress={handleClose}
                style={styles.cancelButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </Col>
  );
};

const styles = StyleSheet.create({
  text: {
    marginLeft: theme.spacing.sm,
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.xl,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  modal: {
    backgroundColor: "white",
    borderRadius: theme.radii.xl,
    padding: 16,
    width: "90%",
    maxHeight: "80%",
    alignItems: "center",
  },
  title: {
    fontWeight: "bold",
    fontSize: 18,
    marginBottom: 12,
  },
  row: {
    flexDirection: "row",
    flexWrap: "wrap",
    maxHeight: 240,
    marginBottom: 12,
    justifyContent: "center",
  },
  option: {
    padding: 8,
    borderBottomWidth: 0.5,
    borderColor: "#eee",
    alignItems: "center",
    minWidth: 70,
    margin: 2,
    borderRadius: 6,
    backgroundColor: "#fff",
  },
  selectedStart: {
    backgroundColor: theme.colors.primary,
  },
  selectedEnd: {
    backgroundColor: theme.colors.primaryDark,
  },
  inRange: {
    backgroundColor: theme.colors.primaryDark,
  },
  disabledOption: {
    backgroundColor: theme.colors.primaryLight,
  },
});
