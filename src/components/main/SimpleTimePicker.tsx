import { useState, useMemo, useCallback } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
import { getStringTime } from "../../utils/date-time.utils";
import { Row } from "./Row";
import { Ionicons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Col } from "./Col";

export interface SimpleTimePickerProps {
  maxTimeOfStay: number;
  start: string; // ej. "09:00"
  end: string; // ej. "21:00"
  onChange?: (value: {
    startTime: string | null;
    endTime: string | null;
  }) => void;
}

// Utilidades extraídas para reducir complejidad
const toMinutes = (time: string): number => {
  const [h, m] = time.split(":").map(Number);
  return h * 60 + m;
};

const calculateTimeDifference = (start: string, end: string): number => {
  return (toMinutes(end) - toMinutes(start)) / 60;
};

const isValidTimeRange = (
  start: string,
  end: string,
  maxHours: number
): boolean => {
  const diff = calculateTimeDifference(start, end);
  return diff >= 1 && diff <= maxHours;
};

const showInvalidRangeAlert = (maxTimeOfStay: number): void => {
  Alert.alert(
    "Rango inválido",
    `Selecciona al menos 1 hora y máximo ${maxTimeOfStay} horas.`
  );
};

// Función para determinar si una opción está deshabilitada
const isOptionDisabled = (
  label: string,
  startTime: string | null,
  endTime: string | null,
  allPossibleSelected: boolean,
  maxTimeOfStay: number
): boolean => {
  if (allPossibleSelected && startTime && endTime) {
    return (
      toMinutes(label) < toMinutes(startTime) ||
      toMinutes(label) > toMinutes(endTime)
    );
  }

  if (startTime && !endTime) {
    if (toMinutes(label) < toMinutes(startTime)) {
      return true;
    }

    const diff = calculateTimeDifference(startTime, label);
    return (
      toMinutes(label) > toMinutes(startTime) &&
      (diff > maxTimeOfStay || diff < 1)
    );
  }

  return false;
};

// Componente para renderizar cada opción de tiempo
interface TimeOptionProps {
  label: string;
  startTime: string | null;
  endTime: string | null;
  allPossibleSelected: boolean;
  maxTimeOfStay: number;
  isInRange: (label: string) => boolean;
  onPress: (label: string) => void;
}

const TimeOption: React.FC<TimeOptionProps> = ({
  label,
  startTime,
  endTime,
  allPossibleSelected,
  maxTimeOfStay,
  isInRange,
  onPress,
}) => {
  const selectedAsStart = startTime === label;
  const selectedAsEnd = endTime === label;
  const between = isInRange(label);
  const disabled = isOptionDisabled(
    label,
    startTime,
    endTime,
    allPossibleSelected,
    maxTimeOfStay
  );

  // Determinar el color del texto basado en el estado
  let textColor = theme.colors.gray700;
  let fontWeight: "normal" | "600" = "normal";

  if (disabled) {
    textColor = theme.colors.gray500;
  } else if (selectedAsStart || selectedAsEnd || between) {
    textColor = theme.colors.white;
    fontWeight = "600";
  }

  return (
    <TouchableOpacity
      style={[
        styles.option,
        selectedAsStart && styles.selectedStart,
        selectedAsEnd && styles.selectedEnd,
        between && styles.inRange,
        disabled && styles.disabledOption,
      ]}
      disabled={disabled}
      onPress={() => onPress(label)}
      activeOpacity={0.7}
    >
      <Text
        style={{
          fontSize: theme.fontSizes.sm,
          color: textColor,
          fontWeight,
        }}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

export const SimpleTimePicker: React.FC<SimpleTimePickerProps> = ({
  start,
  end,
  maxTimeOfStay,
  onChange,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [startTime, setStartTime] = useState<string | null>(null);
  const [endTime, setEndTime] = useState<string | null>(null);

  // Memoizar valores calculados
  const { hours, allPossibleSelected } = useMemo(() => {
    const openTimeForArray = Number(getStringTime(start).split(":")[0]);
    const closeTimeForArray = Number(getStringTime(end).split(":")[0]);

    const hoursArray = Array.from(
      { length: closeTimeForArray - openTimeForArray + 1 },
      (_, i) => i + openTimeForArray
    );

    let allSelected = false;
    if (startTime && endTime) {
      const spaces = calculateTimeDifference(startTime, endTime);
      allSelected = spaces === maxTimeOfStay;
    }

    return {
      hours: hoursArray,
      allPossibleSelected: allSelected,
    };
  }, [start, end, startTime, endTime, maxTimeOfStay]);

  // Rango seleccionado: [startTime, endTime)
  const isInRange = useCallback(
    (label: string) => {
      if (startTime && endTime) {
        return (
          toMinutes(label) >= toMinutes(startTime) &&
          toMinutes(label) < toMinutes(endTime)
        );
      }
      return false;
    },
    [startTime, endTime]
  );

  // Funciones auxiliares para manejar la selección
  const handleNoSelection = useCallback((label: string) => {
    setStartTime(label);
    setEndTime(null);
  }, []);

  const handleStartOnlySelection = useCallback(
    (label: string) => {
      if (!startTime) return;

      const labelMinutes = toMinutes(label);
      const startMinutes = toMinutes(startTime);

      if (labelMinutes < startMinutes) {
        setStartTime(label);
        setEndTime(null);
      } else if (labelMinutes === startMinutes) {
        setStartTime(null);
        setEndTime(null);
      } else {
        if (!isValidTimeRange(startTime, label, maxTimeOfStay)) {
          showInvalidRangeAlert(maxTimeOfStay);
          return;
        }
        setEndTime(label);
      }
    },
    [startTime, maxTimeOfStay]
  );

  const handleBothSelectedCase = useCallback(
    (label: string) => {
      if (!startTime || !endTime) return;

      const labelMinutes = toMinutes(label);
      const startMinutes = toMinutes(startTime);

      if (labelMinutes === startMinutes) {
        setStartTime(null);
        setEndTime(null);
        return;
      }

      if (labelMinutes < startMinutes) {
        setStartTime(label);
        setEndTime(null);
      } else if (labelMinutes > startMinutes) {
        const diff = calculateTimeDifference(startTime, label);
        if (diff > maxTimeOfStay) {
          setStartTime(null);
          setEndTime(null);
          return;
        }
        if (diff < 1) {
          showInvalidRangeAlert(maxTimeOfStay);
          return;
        }
        setEndTime(label);
      }
    },
    [startTime, endTime, maxTimeOfStay]
  );

  // Lógica de selección simplificada
  const handleOptionPress = useCallback(
    (label: string) => {
      if (!startTime && !endTime) {
        handleNoSelection(label);
      } else if (startTime && !endTime) {
        handleStartOnlySelection(label);
      } else if (startTime && endTime) {
        handleBothSelectedCase(label);
      }
    },
    [
      startTime,
      endTime,
      handleNoSelection,
      handleStartOnlySelection,
      handleBothSelectedCase,
    ]
  );

  // Confirmar selección
  const handleConfirm = useCallback(() => {
    if (!startTime || !endTime) {
      Alert.alert("Selecciona inicio y fin");
      return;
    }
    setShowModal(false);
    if (onChange) onChange({ startTime, endTime });
  }, [startTime, endTime, onChange]);

  // Limpiar selección al cerrar modal
  const handleClose = useCallback(() => {
    setShowModal(false);
    setStartTime(null);
    setEndTime(null);
  }, []);

  // Abrir modal
  const handleOpenModal = useCallback(() => {
    setShowModal(true);
  }, []);

  // Memoizar el texto mostrado
  const displayText = useMemo(() => {
    if (startTime && endTime) {
      const hours = calculateTimeDifference(startTime, endTime);
      return `${startTime} - ${endTime} (${hours}h)`;
    }
    return "Seleccionar horario";
  }, [startTime, endTime]);

  return (
    <Col style={{ paddingTop: theme.spacing.md }}>
      <TouchableOpacity onPress={handleOpenModal}>
        <Row align="center">
          <Ionicons
            name="time-outline"
            size={theme.fontSizes.md}
            color={theme.colors.gray700}
          />
          <Text style={styles.text}>{displayText}</Text>
        </Row>
      </TouchableOpacity>

      <Modal visible={showModal} transparent animationType="fade">
        <View style={styles.overlay}>
          <View style={styles.modal}>
            <Text style={styles.title}>Selecciona inicio y fin</Text>
            <View style={styles.row}>
              {hours.map((h) => {
                const label = `${h.toString().padStart(2, "0")}:00`;
                return (
                  <TimeOption
                    key={label}
                    label={label}
                    startTime={startTime}
                    endTime={endTime}
                    allPossibleSelected={allPossibleSelected}
                    maxTimeOfStay={maxTimeOfStay}
                    isInRange={isInRange}
                    onPress={handleOptionPress}
                  />
                );
              })}
            </View>
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                onPress={handleConfirm}
                disabled={!startTime || !endTime}
                style={[
                  styles.confirmButton,
                  (!startTime || !endTime) && styles.confirmButtonDisabled,
                ]}
                activeOpacity={0.8}
              >
                <Text style={styles.confirmButtonText}>Confirmar horario</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleClose}
                style={styles.cancelButton}
                activeOpacity={0.8}
              >
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </Col>
  );
};

const styles = StyleSheet.create({
  text: {
    marginLeft: theme.spacing.sm,
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.xl,
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primaryDark,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modal: {
    backgroundColor: theme.colors.white,
    borderTopLeftRadius: theme.radii.xl,
    borderTopRightRadius: theme.radii.xl,
    padding: theme.spacing.lg,
    maxHeight: "80%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 5,
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "800",
    marginBottom: theme.spacing.lg,
    color: theme.colors.gold,
    textAlign: "center",
  },
  row: {
    flexDirection: "row",
    flexWrap: "wrap",
    maxHeight: 280,
    marginBottom: theme.spacing.lg,
    justifyContent: "center",
    gap: theme.spacing.xs,
  },
  option: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 80,
    minHeight: 44,
    borderRadius: theme.radii.md,
    backgroundColor: theme.colors.gray100,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  selectedStart: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  selectedEnd: {
    backgroundColor: theme.colors.primaryDark,
    borderColor: theme.colors.primaryDark,
  },
  inRange: {
    backgroundColor: theme.colors.gold,
    borderColor: theme.colors.gold,
  },
  disabledOption: {
    backgroundColor: theme.colors.gray100,
    borderColor: theme.colors.gray200,
    opacity: 0.5,
  },
  buttonContainer: {
    gap: theme.spacing.sm,
    paddingTop: theme.spacing.md,
  },
  confirmButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.radii.xl,
    alignItems: "center",
    justifyContent: "center",
  },
  confirmButtonDisabled: {
    backgroundColor: theme.colors.gray300,
  },
  confirmButtonText: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: theme.colors.gray200,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.radii.xl,
    alignItems: "center",
    justifyContent: "center",
  },
  cancelButtonText: {
    color: theme.colors.gray700,
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
});
