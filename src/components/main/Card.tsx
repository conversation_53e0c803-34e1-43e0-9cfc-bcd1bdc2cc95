import { View, StyleSheet, ViewStyle, StyleProp } from "react-native";
import { theme } from "../../theme/theme";

interface CardProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}

export const Card: React.FC<CardProps> = ({ children, style }) => {
  return <View style={[styles.card, style]}>{children}</View>;
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.colors.primaryLight,
    borderRadius: theme.radii.xl,
    padding: theme.spacing.mdd,
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.1,
    shadowRadius: theme.radii.sm,
    elevation: 5, // para Android
    flexDirection: "row",
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
});
