import React from "react";
import { Text, TextStyle, StyleSheet } from "react-native";

type TitleSize = "xl" | "l" | "m" | "s" | "xs" | "ss";

interface TitleProps {
  children: React.ReactNode;
  size?: TitleSize;
  center?: boolean;
  style?: TextStyle;
}

const sizeStyles: Record<TitleSize, TextStyle> = {
  xl: { fontSize: 32, lineHeight: 40 },
  l: { fontSize: 24, lineHeight: 32 },
  m: { fontSize: 20, lineHeight: 28 },
  s: { fontSize: 16, lineHeight: 24 },
  ss: { fontSize: 12, lineHeight: 14 },
  xs: { fontSize: 8, lineHeight: 12 },
};

export const Title: React.FC<TitleProps> = ({
  children,
  size = "l",
  center = false,
  style,
}) => {
  return (
    <Text
      style={[styles.base, sizeStyles[size], center && styles.center, style]}
    >
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  base: {
    fontWeight: "800",
    color: "#111827",
  },
  center: {
    textAlign: "center",
  },
});
