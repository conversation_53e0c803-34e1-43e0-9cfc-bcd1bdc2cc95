import { Title } from "./Title";
import { StyleSheet, Text, View } from "react-native";
import { theme } from "../../theme";
import { Row } from "./Row";

interface SectionProps {
  title?: string;
  create?: boolean;
  children: React.ReactElement;
}

export const Section: React.FC<SectionProps> = ({
  title,
  children,
  create,
}) => {
  return (
    <View style={styles.container}>
      <Row style={{ justifyContent: "space-between" }}>
        <Title size="s" style={styles.title}>
          {title}
        </Title>
        {create && <Text>Crear</Text>}
      </Row>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.sm,
    marginTop: theme.spacing.md,
  },
  title: { color: theme.colors.gold },
});
