import React from "react";
import {
  View,
  ViewProps,
  StyleSheet,
  ViewStyle,
  StyleProp,
} from "react-native";

interface RowProps extends ViewProps {
  gap?: number;
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
  align?: ViewStyle["alignItems"];
  justify?: ViewStyle["justifyContent"];
  wrap?: ViewStyle["flexWrap"];
}

export const Row: React.FC<RowProps> = ({
  gap = 0,
  style,
  children,
  align,
  justify,
  wrap,
  ...rest
}) => {
  return (
    <View
      style={[
        styles.row,
        {
          columnGap: gap,
          alignItems: align,
          justifyContent: justify,
          flexWrap: wrap,
        },
        style,
      ]}
      {...rest}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
  },
});
