// src/components/ui/Button.tsx
import React from "react";
import {
  Text,
  TouchableOpacity,
  TouchableOpacityProps,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";
import { theme } from "../../../theme";

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

export const Button: React.FC<ButtonProps> = ({
  title,
  icon,
  iconPosition = "left",
  ...props
}) => {
  const isRight = iconPosition === "right";

  return (
    <TouchableOpacity style={styles.button} {...props}>
      <View style={[styles.content, isRight && styles.rowReverse]}>
        {icon && <View style={styles.icon}>{icon}</View>}
        <Text style={styles.text}>{title}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.radii.xl,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: theme.spacing.sm,
  } as ViewStyle,
  rowReverse: {
    flexDirection: "row-reverse",
  },
  icon: {
    marginHorizontal: theme.spacing.sm / 2,
  },
  text: {
    color: theme.colors.white,
    fontWeight: "600",
    fontSize: theme.fontSizes.sm,
  },
});
