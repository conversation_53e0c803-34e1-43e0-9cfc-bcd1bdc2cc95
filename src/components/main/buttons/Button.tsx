// src/components/ui/Button.tsx
import React from "react";
import {
  Text,
  TouchableOpacity,
  TouchableOpacityProps,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";
import { theme } from "../../../theme";

type ButtonType = "Primary" | "Secondary" | "Danger" | "Disabled";

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  type?: ButtonType;
}

const getBackgroundColor = (type: ButtonType) => {
  if (type === "Danger") return theme.colors.error;
  if (type === "Disabled") return theme.colors.gray200;
  if (type === "Secondary") return theme.colors.gray200;
  return theme.colors.primary;
};

const getTextColor = (type: ButtonType) => {
  if (type === "Secondary") return theme.colors.gray700;
  if (type === "Danger") return theme.colors.white;
  if (type === "Disabled") return theme.colors.gray500;
  return theme.colors.white;
};

export const Button: React.FC<ButtonProps> = ({
  title,
  icon,
  iconPosition = "left",
  type,
  ...props
}) => {
  const isRight = iconPosition === "right";

  //Color is not seting font color
  const textStyle = [styles.text, type && { color: getTextColor(type) }];

  const mainStyle = [
    styles.button,
    type && {
      backgroundColor: getBackgroundColor(type),
    },
  ];

  return (
    <TouchableOpacity style={mainStyle} {...props}>
      <View style={[styles.content, isRight && styles.rowReverse]}>
        {icon && <View style={styles.icon}>{icon}</View>}
        <Text style={textStyle}>{title}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.smd,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.radii.xl,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: theme.spacing.sm,
  } as ViewStyle,
  rowReverse: {
    flexDirection: "row-reverse",
  },
  icon: {
    marginHorizontal: theme.spacing.sm / 2,
  },
  text: {
    color: theme.colors.white,
    fontWeight: "600",
    fontSize: theme.fontSizes.sm,
  },
});
