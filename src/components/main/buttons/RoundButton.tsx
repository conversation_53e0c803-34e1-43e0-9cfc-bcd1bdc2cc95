import React from "react";
import {
  TouchableOpacity,
  TouchableOpacityProps,
  StyleSheet,
  View,
} from "react-native";
import { theme } from "../../../theme";

interface RoundButtonProps extends TouchableOpacityProps {
  icon: React.ReactNode;
}

export const RoundButton: React.FC<RoundButtonProps> = ({ icon, ...props }) => {
  return (
    <TouchableOpacity style={styles.button} {...props}>
      {icon && <View style={styles.content}>{icon}</View>}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: theme.colors.primary,
    width: 42,
    height: 42,
    borderRadius: theme.radii.xl,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
});
