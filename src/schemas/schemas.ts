import { z } from "zod";

export type LoginFormValues = z.infer<typeof loginSchema>;

export const loginSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
  password: z
    .string({ required_error: "Contraseña requerida" })
    .min(6, "Mínimo 6 caracteres"),
});

export type ReservationFormValues = z.infer<typeof reservationSchema>;

export const reservationSchema = z.object({
  amountOfPeople: z
    .string({ required_error: "Cantidad de personas requerida" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "Debe ser un número válido mayor o igual a 1",
    }),
  startDateTime: z
    .string({ required_error: "Fecha y hora de inicio requerida" })
    .datetime("Fecha y hora de inicio inválida"),
  endDateTime: z
    .string({ required_error: "Fecha y hora de fin requerida" })
    .datetime("Fecha y hora de fin inválida"),
  hoursRequested: z
    .string({ required_error: "Horas solicitadas requeridas" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "Debe ser un número válido mayor o igual a 1",
    }),
});
