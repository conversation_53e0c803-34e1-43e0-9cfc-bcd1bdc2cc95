import { z } from "zod";

export type LoginFormValues = z.infer<typeof loginSchema>;

export const loginSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
  password: z
    .string({ required_error: "Contraseña requerida" })
    .min(6, "Mínimo 6 caracteres"),
});

export type ReservationFormValues = z.infer<typeof reservationSchema>;

export const reservationSchema = z.object({
  amountOfPeople: z
    .string({ required_error: "Cantidad de personas requerida" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "Debe ser un número válido mayor o igual a 1",
    }),
  hoursRequested: z
    .string({ required_error: "Horas solicitadas requeridas" })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: "Debe ser un número válido mayor o igual a 1",
    }),
  // Campos opcionales para debugging
  startDateTime: z.string().optional(),
  endDateTime: z.string().optional(),
});
