import { z } from "zod";

export type LoginFormValues = z.infer<typeof loginSchema>;

export const loginSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
  password: z
    .string({ required_error: "Contraseña requerida" })
    .min(6, "Mínimo 6 caracteres"),
});

export type ReservationFormValues = z.infer<typeof reservationSchema>;

export const reservationSchema = z.object({
  amountOfPeople: z
    .number({ required_error: "Cantidad de personas requerida" })
    .min(1, "Mínimo 1 persona"),
  startDateTime: z
    .string({ required_error: "Fecha y hora de inicio requerida" })
    .datetime("Fecha y hora de inicio inválida"),
  endDateTime: z
    .string({ required_error: "Fecha y hora de fin requerida" })
    .datetime("Fecha y hora de fin inválida"),
});
