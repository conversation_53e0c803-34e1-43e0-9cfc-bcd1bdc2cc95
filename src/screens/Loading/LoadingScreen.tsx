// src/screens/LoadingScreen.tsx
import React from "react";
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Button,
} from "react-native";
import { executeLogout } from "../../api/auth/auth-handle";

export const LoadingScreen = () => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#237A91" />
      <Text style={styles.text}>Cargando...</Text>
      <Button title="Cerrar sesión" onPress={executeLogout} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
    paddingHorizontal: 20,
  },
  text: {
    fontSize: 20,
    fontWeight: "600",
    color: "#111827",
  },
});
