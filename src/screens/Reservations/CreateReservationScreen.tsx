import { useRoute } from "@react-navigation/native";
import { useEffect, useMemo, useState } from "react";
import { View, StyleSheet } from "react-native";
import { CreateReservationRouteType } from "../../navigation/types";
import { DateTime } from "luxon";
import { <PERSON><PERSON>, Row } from "../../components";
import { theme } from "../../theme";
import { getTimeForJSDate } from "../../utils/date-time.utils";
import { Picker } from "@react-native-picker/picker";
import { FormField } from "../../components/forms/FormField";
import { Ionicons } from "@expo/vector-icons";
import { useZodForm } from "../../hooks/useZodForm";
import {
  ReservationFormValues,
  reservationSchema,
} from "../../schemas/schemas";
import { GradientView } from "../../components/layouts/GradientView";
import { RoundButton } from "../../components/main/buttons/RoundButton";

export const CreateReservationScreen: React.FC = () => {
  const { params } = useRoute<CreateReservationRouteType>();
  const [hoursRequested, setHoursRequested] = useState<number>(1);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string | null>(
    null
  );
  const { control, handleSubmit, watch } = useZodForm(reservationSchema, {
    defaultValues: {
      hoursRequested: "1" as any,
      amountOfPeople: "1" as any,
    },
  });

  const onSubmit = (data: ReservationFormValues) => {
    if (!selectedTimeRange) {
      console.warn("Por favor selecciona un rango de tiempo");
      // Aquí podrías mostrar un alert o toast
      return;
    }

    console.log("Datos del formulario:", data);
    console.log("Rango de tiempo seleccionado:", selectedTimeRange);
    console.log("Parámetros de la amenidad:", {
      facilityId: params.id,
      facilityName: params.name,
      selectedDate: params.selectedDate,
      maxPeople: params.maxAmountOfPeople,
      maxTimeOfStay: params.maxTimeOfStay,
    });

    // Aquí puedes agregar la lógica para enviar la reservación
    // Por ejemplo: createReservation({ ...data, selectedTimeRange, facilityId: params.id })
  };

  // Observar cambios en hoursRequested
  const watchedHoursRequested = watch("hoursRequested");
  const watchedAmountOfPeople = watch("amountOfPeople");

  // Actualizar el estado local cuando cambie el valor del formulario
  useEffect(() => {
    const hours = Number(watchedHoursRequested) || 1;
    setHoursRequested(hours);
  }, [watchedHoursRequested, watchedAmountOfPeople]);

  const availableRanges = useMemo(() => {
    const openTime = getTimeForJSDate(params.open);
    const closeTime = getTimeForJSDate(params.close);

    const start = DateTime.fromISO(`${params.selectedDate}T${openTime}`);
    const end = DateTime.fromISO(`${params.selectedDate}T${closeTime}`);

    const stepInMinutes = hoursRequested * 60;
    const ranges: { start: string; end: string }[] = [];

    let current = start;

    while (current.plus({ minutes: stepInMinutes }) <= end) {
      const endTime = current.plus({ minutes: stepInMinutes });

      ranges.push({
        start: current.toFormat("HH:mm"),
        end: endTime.toFormat("HH:mm"),
      });

      current = current.plus({ minutes: 15 }); // avanzar 15 min
    }

    return ranges;
  }, [hoursRequested, params.open, params.close, params.selectedDate]);

  return (
    <GradientView firstLineText={params.name}>
      <View style={styles.container}>
        <Row
          gap={theme.spacing.md}
          justify="space-between"
          style={{ width: "100%" }}
        >
          <RoundButton
            icon={
              <Ionicons
                name="remove-outline"
                size={theme.fontSizes.md}
                color={theme.colors.white}
              />
            }
            onPress={() => setHoursRequested((prev) => Math.max(prev - 1, 1))}
          />
          <View style={{ flex: 1, marginHorizontal: theme.spacing.sm }}>
            <FormField
              control={control}
              name="amountOfPeople"
              placeholder="Cantidad de personas"
              keyboardType="numeric"
              icon={
                <Ionicons
                  name="people-outline"
                  size={theme.fontSizes.md}
                  color={theme.colors.gray700}
                />
              }
            />
          </View>
          <RoundButton
            icon={
              <Ionicons
                name="add-outline"
                size={theme.fontSizes.md}
                color={theme.colors.white}
              />
            }
            onPress={() => setHoursRequested((prev) => prev + 1)}
          />
        </Row>

        <FormField
          control={control}
          name="hoursRequested"
          placeholder="Horas solicitadas"
          keyboardType="numeric"
          icon={
            <Ionicons
              name="time-outline"
              size={theme.fontSizes.md}
              color={theme.colors.gray700}
            />
          }
        />

        <Picker
          selectedValue={selectedTimeRange}
          onValueChange={(value) => setSelectedTimeRange(value)}
          style={styles.picker}
        >
          <Picker.Item
            label="Selecciona un horario..."
            value={null}
            color={theme.colors.gray500}
          />
          {availableRanges.map((range) => (
            <Picker.Item
              key={`${range.start}-${range.end}`}
              label={`${range.start} - ${range.end}`}
              value={`${range.start}-${range.end}`}
            />
          ))}
        </Picker>

        <View style={styles.button}>
          <Button title="Reservar" onPress={handleSubmit(onSubmit)} />
        </View>
      </View>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    borderBottomWidth: 1,
    marginBottom: 20,
    paddingVertical: 4,
  },
  range: {
    fontSize: 14,
    marginVertical: 2,
  },
  empty: {
    fontStyle: "italic",
    color: "#888",
    marginTop: 4,
  },
  picker: {
    backgroundColor: theme.colors.gray100,
    borderRadius: theme.radii.md,
    marginBottom: theme.spacing.md,
  },
  button: {
    marginTop: 32,
  },
});
