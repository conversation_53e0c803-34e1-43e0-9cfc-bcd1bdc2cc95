import { useNavigation, useRoute } from "@react-navigation/native";
import React, { useEffect, useState, useMemo } from "react";
import { View, StyleSheet, Alert } from "react-native";
import { CreateReservationRouteType } from "../../navigation/types";
import { <PERSON><PERSON>, Row } from "../../components";
import { theme } from "../../theme";
import { FormField } from "../../components/forms/FormField";
import { Ionicons } from "@expo/vector-icons";
import { useZodForm } from "../../hooks/useZodForm";
import {
  ReservationFormValues,
  reservationSchema,
} from "../../schemas/schemas";
import { GradientView } from "../../components/layouts/GradientView";
import { RoundButton } from "../../components/main/buttons/RoundButton";
import { useReservations } from "../../hooks/useReservations";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { QUERIES } from "../../constants/queries";
import { CreateReservation } from "../../interfaces/reservation";
import { ReservationConfirmModal } from "../../components/modals/ReservationConfirmModal";
import { SuccessModal } from "../../components/modals/SuccessModal";
import { ErrorModal } from "../../components/modals/ErrorModal";
import { LoadingOverlay } from "../../components/LoadingOverlay";
import { SimpleTimePicker } from "../../components/main/SimpleTimePicker";
import { DateTime } from "luxon";

export const CreateReservationScreen: React.FC = () => {
  const navigation = useNavigation();
  const { params } = useRoute<CreateReservationRouteType>();
  const [hoursRequested, setHoursRequested] = useState<number>(1);
  const [amountOfPeople, setAmountOfPeople] = useState<number>(1);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string | null>(
    null
  );

  // Estados para modales
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Hooks
  const { createReservation } = useReservations();
  const me = useCachedQuery<Me>(QUERIES.ME);
  const propertyId = me.data?.properties[0].id ?? "";

  const { control, handleSubmit, watch, setValue } = useZodForm(
    reservationSchema,
    {
      defaultValues: {
        hoursRequested: "1" as any,
        amountOfPeople: "1" as any,
        selectedTimeRange: "" as any,
      },
    }
  );

  const onSubmit = (_: ReservationFormValues) => {
    // Validación adicional
    if (!selectedTimeRange) {
      Alert.alert("Error", "Por favor selecciona un horario");
      return;
    }

    // Mostrar modal de confirmación
    setShowConfirmModal(true);
  };

  // Observar cambios en los campos
  const watchedHoursRequested = watch("hoursRequested");
  const watchedAmountOfPeople = watch("amountOfPeople");

  // Actualizar el estado local cuando cambie el valor del formulario
  useEffect(() => {
    const hours = Number(watchedHoursRequested) || 1;
    setHoursRequested(hours);
  }, [watchedHoursRequested]);

  useEffect(() => {
    const people = Number(watchedAmountOfPeople) || 1;
    setAmountOfPeople(people);
  }, [watchedAmountOfPeople]);

  // Funciones para manejar los botones
  const handleAmountOfPeopleChange = (newValue: number) => {
    const min = 1;
    const max = params.maxAmountOfPeople ?? Infinity;
    const value = Math.max(min, Math.min(newValue, max));
    setAmountOfPeople(value);
    setValue("amountOfPeople", value.toString() as any);
  };

  const handleHoursRequestedChange = (newValue: number) => {
    const min = 1;
    const max = params.maxTimeOfStay ?? Infinity;
    const value = Math.max(min, Math.min(newValue, max));
    setHoursRequested(value);
    setValue("hoursRequested", value.toString() as any);
  };

  // Función para manejar la selección de tiempo
  const handleTimeRangeChange = (timeRange: {
    startTime: string | null;
    endTime: string | null;
  }) => {
    if (timeRange.startTime && timeRange.endTime) {
      const rangeString = `${timeRange.startTime}-${timeRange.endTime}`;
      setSelectedTimeRange(rangeString);
      setValue("selectedTimeRange", rangeString);
    }
  };

  // Función para confirmar la reservación
  const handleConfirmReservation = async () => {
    if (!selectedTimeRange || !propertyId) return;

    try {
      // Convertir el rango de tiempo seleccionado a fechas UTC
      const [startTime, endTime] = selectedTimeRange.split("-");
      const startDateTime = DateTime.fromISO(
        `${params.selectedDate}T${startTime}:00`
      ).toUTC();
      const endDateTime = DateTime.fromISO(
        `${params.selectedDate}T${endTime}:00`
      ).toUTC();

      const reservationData: CreateReservation = {
        propertyId,
        facilityId: params.id,
        amountOfPeople,
        startDateTime: startDateTime.toISO()!,
        endDateTime: endDateTime.toISO()!,
      };

      await createReservation.mutateAsync(reservationData);

      // Cerrar modal de confirmación y mostrar éxito
      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error: any) {
      // Cerrar modal de confirmación y mostrar error
      setShowConfirmModal(false);
      setErrorMessage(
        error?.response?.data?.message ?? "Error al crear la reservación"
      );
      setShowErrorModal(true);
    }
  };

  // Función para cerrar modal de éxito y navegar de vuelta
  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  // Función para cerrar modal de error
  const handleErrorClose = () => {
    setShowErrorModal(false);
  };

  // Preparar datos para el modal de confirmación
  const reservationData = useMemo(() => {
    if (!selectedTimeRange) return null;

    const [startTime, endTime] = selectedTimeRange.split("-");
    const startDateTime = DateTime.fromISO(
      `${params.selectedDate}T${startTime}:00`
    ).toUTC();
    const endDateTime = DateTime.fromISO(
      `${params.selectedDate}T${endTime}:00`
    ).toUTC();

    return {
      facilityName: params.name,
      amountOfPeople,
      startDateTime: startDateTime.toISO()!,
      endDateTime: endDateTime.toISO()!,
      selectedDate: params.selectedDate,
    };
  }, [selectedTimeRange, amountOfPeople, params]);

  return (
    <GradientView firstLineText={params.name}>
      <View style={styles.container}>
        {/* Cantidad de personas */}
        <Row
          gap={theme.spacing.md}
          justify="space-between"
          style={{ width: "100%" }}
        >
          <RoundButton
            icon={
              <Ionicons
                name="remove-outline"
                size={theme.fontSizes.md}
                color={theme.colors.white}
              />
            }
            onPress={() => handleAmountOfPeopleChange(amountOfPeople - 1)}
          />
          <View style={{ flex: 1, marginHorizontal: theme.spacing.sm }}>
            <FormField
              control={control}
              name="amountOfPeople"
              placeholder="Cantidad de personas"
              keyboardType="numeric"
              icon={
                <Ionicons
                  name="people-outline"
                  size={theme.fontSizes.md}
                  color={theme.colors.gray700}
                />
              }
            />
          </View>
          <RoundButton
            icon={
              <Ionicons
                name="add-outline"
                size={theme.fontSizes.md}
                color={theme.colors.white}
              />
            }
            onPress={() => handleAmountOfPeopleChange(amountOfPeople + 1)}
          />
        </Row>

        {/* Horas solicitadas */}
        <Row
          gap={theme.spacing.md}
          justify="space-between"
          style={{ width: "100%" }}
        >
          <RoundButton
            icon={
              <Ionicons
                name="remove-outline"
                size={theme.fontSizes.md}
                color={theme.colors.white}
              />
            }
            onPress={() => handleHoursRequestedChange(hoursRequested - 1)}
          />
          <View style={{ flex: 1, marginHorizontal: theme.spacing.sm }}>
            <FormField
              control={control}
              name="hoursRequested"
              placeholder="Horas solicitadas"
              keyboardType="numeric"
              icon={
                <Ionicons
                  name="time-outline"
                  size={theme.fontSizes.md}
                  color={theme.colors.gray700}
                />
              }
            />
          </View>
          <RoundButton
            icon={
              <Ionicons
                name="add-outline"
                size={theme.fontSizes.md}
                color={theme.colors.white}
              />
            }
            onPress={() => handleHoursRequestedChange(hoursRequested + 1)}
          />
        </Row>

        {/* SimpleTimePicker */}
        <SimpleTimePicker
          maxTimeOfStay={params.maxTimeOfStay ?? 8}
          start={params.open}
          end={params.close}
          onChange={handleTimeRangeChange}
        />

        <Button title="Reservar" onPress={handleSubmit(onSubmit)} />
      </View>

      {/* Loading Overlay */}
      <LoadingOverlay visible={createReservation.isPending} />

      {/* Modal de Confirmación */}
      <ReservationConfirmModal
        visible={showConfirmModal}
        reservationData={reservationData}
        onConfirm={handleConfirmReservation}
        onCancel={() => setShowConfirmModal(false)}
        isLoading={createReservation.isPending}
      />

      {/* Modal de Éxito */}
      <SuccessModal
        visible={showSuccessModal}
        title="¡Reservación Exitosa!"
        message="Tu reservación ha sido creada exitosamente. Recibirás una confirmación pronto."
        onClose={handleSuccessClose}
        buttonText="Continuar"
      />

      {/* Modal de Error */}
      <ErrorModal
        visible={showErrorModal}
        title="Error en la Reservación"
        message={errorMessage}
        onClose={handleErrorClose}
        buttonText="Intentar de nuevo"
      />
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    borderBottomWidth: 1,
    marginBottom: 20,
    paddingVertical: 4,
  },
});
