import { useNavigation, useRoute } from "@react-navigation/native";
import React, { useMemo, useState } from "react";
import { View, StyleSheet, TextInput } from "react-native";
import { CreateReservationRouteType } from "../../navigation/stacks/ReservationsStack";
import { DateTime } from "luxon";
import { Button } from "../../components";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { FacilitiesStackParamList } from "../../navigation/stacks/FacilitiesStack";
import { theme } from "../../theme";
import { getTimeForJSDate } from "../../utils/date-time.utils";
import { Picker } from "@react-native-picker/picker";
import { FormField } from "../../components/forms/FormField";
import { useZodForm } from "../../hooks/useZodForm";
import {
  ReservationFormValues,
  reservationSchema,
} from "../../schemas/schemas";
import { GradientView } from "../../components/layouts/GradientView";

export const ReservationScreen: React.FC = () => {
  const { params } = useRoute<CreateReservationRouteType>();
  const navigation =
    useNavigation<NativeStackNavigationProp<FacilitiesStackParamList>>();
  const [hoursRequested, setHoursRequested] = useState<number>(1);
  const [people, setPeople] = useState<number>(1);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string | null>(
    null
  );
  const { control, handleSubmit } = useZodForm(reservationSchema);
  const onSubmit = (data: ReservationFormValues) => {
    console.log(data);
  };

  const availableRanges = useMemo(() => {
    const openTime = getTimeForJSDate(params.open);
    const closeTime = getTimeForJSDate(params.close);

    const start = DateTime.fromISO(`${params.selectedDate}T${openTime}`);
    const end = DateTime.fromISO(`${params.selectedDate}T${closeTime}`);

    const stepInMinutes = hoursRequested * 60;
    const ranges: { start: string; end: string }[] = [];

    let current = start;

    while (current.plus({ minutes: stepInMinutes }) <= end) {
      const endTime = current.plus({ minutes: stepInMinutes });

      ranges.push({
        start: current.toFormat("HH:mm"),
        end: endTime.toFormat("HH:mm"),
      });

      current = current.plus({ minutes: 15 }); // avanzar 15 min
    }

    return ranges;
  }, [hoursRequested, params.open, params.close, params.selectedDate]);

  return (
    <GradientView firstLineText={params.name}>
      <View style={styles.container}>
        {/* <Text style={styles.title}>{params.name}</Text> */}

        <FormField
          control={control}
          name="amountOfPeople"
          placeholder="Cantidad de personas"
          keyboardType="numeric"
          // icon={

          // }
        />

        <TextInput
          keyboardType="numeric"
          value={hoursRequested.toString()}
          onChangeText={(v) => setHoursRequested(Number(v))}
          style={styles.input}
        />

        <Picker
          selectedValue={selectedTimeRange}
          onValueChange={(value) => setSelectedTimeRange(value)}
        >
          {availableRanges.map((r, i) => (
            <Picker.Item
              key={i}
              label={`${r.start} - ${r.end}`}
              value={`${r.start}-${r.end}`}
            />
          ))}
        </Picker>

        <View style={styles.button}>
          <Button title="Volver" onPress={() => navigation.goBack()} />
        </View>
      </View>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    borderBottomWidth: 1,
    marginBottom: 20,
    paddingVertical: 4,
  },
  range: {
    fontSize: 14,
    marginVertical: 2,
  },
  empty: {
    fontStyle: "italic",
    color: "#888",
    marginTop: 4,
  },
  button: {
    marginTop: 32,
  },
});
