import { z } from "zod";
import { useZodForm } from "../../hooks/useZodForm";
import { FormField } from "../../components/forms/FormField";
import { Controller } from "react-hook-form";
import { Col } from "../../components/main/Col";
import { TextAreaFormField } from "../../components/forms/TextAreaFormField";

const complaintSchema = z.object({
  email: z
    .string({ required_error: "Correo requerido" })
    .email("Correo inválido"),
  detail: z
    .string({ required_error: "Contraseña requerida" })
    .min(6, "Mínimo 6 caracteres"),
});

type ComplaintFormValues = z.infer<typeof complaintSchema>;

export const CreateComplaintScreen: React.FC = () => {
  const { control, handleSubmit } = useZodForm(complaintSchema);

  const onSubmit = (data: ComplaintFormValues) => {
    console.log(data);
  };

  return (
    <Col>
      <Controller
        control={control}
        name="detail"
        rules={{ required: "Este campo es obligatorio" }}
        render={({ field }) => (
          <TextAreaFormField
            {...field}
            placeholder="Escribe algo aquí..."
            numberOfLines={4}
            control={control}
          />
        )}
      />
    </Col>
  );
};
