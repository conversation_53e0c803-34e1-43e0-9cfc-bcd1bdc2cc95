import { Calendar, DateData } from "react-native-calendars";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { useState, useMemo } from "react";
import { theme } from "../../theme";
import {
  getDayName,
  getDayNumber,
  getStringTimeFromIso,
  getToday,
} from "../../utils/date-time.utils";
import { useRoute } from "@react-navigation/native";
import { Button, Col, Row } from "../../components";
import { GradientView } from "../../components/layouts/GradientView";
import { FacilityRouteType } from "../../navigation";
import { ReservationFormValues } from "../../schemas/schemas";
import { Ionicons } from "@expo/vector-icons";
import { AddReduce } from "../../components/main/AddReduce";
import { SimpleTimePicker } from "../../components/main/SimpleTimePicker";
import { Picker } from "@react-native-picker/picker";

export const FacilityScreen: React.FC = () => {
  const { params } = useRoute<FacilityRouteType>();

  const today = getToday();
  const [selectedDate, setSelectedDate] = useState<string>(today);

  const [amountOfPeople, setAmountOfPeople] = useState<number>(1);

  const facility = params.facility;

  const onSubmit = (data: ReservationFormValues) => {
    console.log(data);
  };

  const handleAmountOfPeopleChange = (newValue: number) => {
    const min = 1;
    const max = facility.maxAmountOfPeople ?? Infinity;
    const value = Math.max(min, Math.min(newValue, max));
    setAmountOfPeople(value);
  };

  const reservations = facility.reservations;

  const markedDates = useMemo(() => {
    const marks: Record<string, any> = {};

    reservations.forEach((r) => {
      const dateKey = r.startDateTime.split("T")[0];

      marks[dateKey] ??= { dots: [] };

      marks[dateKey].dots.push({
        color: theme.colors.primary,
      });
    });

    // Marca el día seleccionado
    if (selectedDate) {
      marks[selectedDate] = {
        ...(marks[selectedDate] ?? {}),
        selected: true,
        selectedColor: theme.colors.primaryDark,
        selectedTextColor: theme.colors.white,
        dots: marks[selectedDate]?.dots ?? [],
      };
    }

    return marks;
  }, [reservations, selectedDate]);

  const dailyReservations = useMemo(() => {
    return reservations
      .filter((r) => {
        const reservationDate =
          r.startDateTime.length > 10
            ? r.startDateTime.split("T")[0]
            : r.startDateTime;
        return reservationDate === selectedDate;
      })
      .sort((a, b) => {
        return a.startDateTime.localeCompare(b.startDateTime);
      });
  }, [reservations, selectedDate]);

  const handleDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
  };

  return (
    <GradientView firstLineText={facility.name}>
      <ScrollView style={styles.container}>
        <Calendar
          onDayPress={handleDayPress}
          markedDates={markedDates}
          markingType="multi-dot"
          minDate={today}
          disableAllTouchEventsForDisabledDays
          theme={{
            todayTextColor: theme.colors.orange.dark,
            selectedDayBackgroundColor: theme.colors.primaryDark,
            arrowColor: theme.colors.primaryDark,
          }}
        />
        <ScrollView>
          {dailyReservations.length > 0 ? (
            dailyReservations.map((reservation, index) => (
              <View
                key={reservation.startDateTime + index}
                style={styles.reservationCard}
              >
                <Text style={styles.timeRange}>
                  {getStringTimeFromIso(reservation.startDateTime)} -{" "}
                  {getStringTimeFromIso(reservation.endDateTime)}
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.noReservations}>
              No hay reservas para este día.
            </Text>
          )}
        </ScrollView>
        <Row style={styles.reservationsContainer}>
          <View style={styles.dateInfo}>
            <Text style={styles.dayNumber}>{getDayNumber(selectedDate)}</Text>
            <Text style={styles.dayName}>{getDayName(selectedDate)}</Text>
            <Text style={styles.reservationsCount}>
              {dailyReservations.length}{" "}
              {dailyReservations.length === 1 ? "reserva" : "reservas"}
            </Text>
          </View>
          <Col style={styles.fieldsContainer}>
            <AddReduce
              value={amountOfPeople}
              onChange={handleAmountOfPeopleChange}
              icon={
                <Ionicons
                  name="people-outline"
                  size={theme.fontSizes.md}
                  color={theme.colors.gray700}
                />
              }
            />
            <SimpleTimePicker
              start={facility.open}
              end={facility.close}
              maxTimeOfStay={facility.maxTimeOfStay ?? Infinity}
              onChange={(value: {
                startTime: string | null;
                endTime: string | null;
              }) => console.log(value.startTime, value.endTime)}
            />
          </Col>
        </Row>

        <Button title="Reservar" onPress={() => onSubmit} />
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  reservationsContainer: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.md,
    fontWeight: "bold",
    marginBottom: theme.spacing.md,
  },
  reservationCard: {
    backgroundColor: theme.colors.primaryLight,
    padding: theme.spacing.sm,
    borderRadius: theme.radii.md,
    marginBottom: theme.spacing.sm,
  },
  facilityName: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primaryDark,
  },
  timeRange: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray700,
    marginTop: theme.spacing.xs,
  },
  noReservations: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginTop: theme.spacing.md,
  },
  dateInfo: {
    alignItems: "center",
    marginBottom: theme.spacing.md,
    marginRight: theme.spacing.md,
  },
  dayNumber: {
    fontSize: theme.fontSizes.xxxl,
    fontWeight: "bold",
    color: theme.colors.primaryDark,
  },
  dayName: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray700,
    textTransform: "capitalize",
    marginTop: theme.spacing.xs,
  },
  reservationsCount: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginTop: theme.spacing.xs,
  },
  fieldsContainer: {
    paddingHorizontal: theme.spacing.md,
  },
});
