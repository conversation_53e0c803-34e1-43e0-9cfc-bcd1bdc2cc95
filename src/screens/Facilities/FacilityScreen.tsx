import { Calendar, DateData } from "react-native-calendars";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { useState, useMemo } from "react";
import { theme } from "../../theme";
import {
  getDayName,
  getDayNumber,
  getStringTimeFromIso,
  getToday,
} from "../../utils/date-time.utils";
import { useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Button, Row } from "../../components";
import { GradientView } from "../../components/layouts/GradientView";
import {
  FACILITIES_SCREENS,
  FacilitiesStackParamList,
  FacilityRouteType,
} from "../../navigation";

export const FacilityScreen: React.FC = () => {
  const { params } = useRoute<FacilityRouteType>();
  const navigation =
    useNavigation<NativeStackNavigationProp<FacilitiesStackParamList>>();
  const today = getToday();
  const [selectedDate, setSelectedDate] = useState<string>(today);

  const facility = params.facility;

  const reservations = facility.reservations;

  const markedDates = useMemo(() => {
    const marks: Record<string, any> = {};

    reservations.forEach((r) => {
      const dateKey = r.startDateTime.split("T")[0];

      marks[dateKey] ??= { dots: [] };

      marks[dateKey].dots.push({
        color: theme.colors.primary,
      });
    });

    // Marca el día seleccionado
    if (selectedDate) {
      marks[selectedDate] = {
        ...(marks[selectedDate] ?? {}),
        selected: true,
        selectedColor: theme.colors.primaryDark,
        selectedTextColor: theme.colors.white,
        dots: marks[selectedDate]?.dots ?? [],
      };
    }

    return marks;
  }, [reservations, selectedDate]);

  const dailyReservations = useMemo(() => {
    return reservations
      .filter((r) => {
        const reservationDate =
          r.startDateTime.length > 10
            ? r.startDateTime.split("T")[0]
            : r.startDateTime;
        return reservationDate === selectedDate;
      })
      .sort((a, b) => {
        return a.startDateTime.localeCompare(b.startDateTime);
      });
  }, [reservations, selectedDate]);

  const handleDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
  };

  return (
    <GradientView firstLineText={facility.name}>
      <ScrollView style={styles.container}>
        <Calendar
          onDayPress={handleDayPress}
          markedDates={markedDates}
          markingType="multi-dot"
          minDate={today}
          disableAllTouchEventsForDisabledDays
          theme={{
            todayTextColor: theme.colors.orange.dark,
            selectedDayBackgroundColor: theme.colors.primaryDark,
            arrowColor: theme.colors.primaryDark,
          }}
        />
        <Row style={styles.reservationsContainer}>
          <View style={styles.dateInfo}>
            <Text style={styles.dayNumber}>{getDayNumber(selectedDate)}</Text>
            <Text style={styles.dayName}>{getDayName(selectedDate)}</Text>
            <Text style={styles.reservationsCount}>
              {dailyReservations.length}{" "}
              {dailyReservations.length === 1 ? "reserva" : "reservas"}
            </Text>
          </View>

          <ScrollView>
            {dailyReservations.length > 0 ? (
              dailyReservations.map((reservation, index) => (
                <View
                  key={reservation.startDateTime + index}
                  style={styles.reservationCard}
                >
                  <Text style={styles.timeRange}>
                    {getStringTimeFromIso(reservation.startDateTime)} -{" "}
                    {getStringTimeFromIso(reservation.endDateTime)}
                  </Text>
                </View>
              ))
            ) : (
              <Text style={styles.noReservations}>
                No hay reservas para este día.
              </Text>
            )}
          </ScrollView>
        </Row>
        <View style={styles.buttonContainer}>
          <Button
            title={`Reservar para el ${getDayName(selectedDate)} ${getDayNumber(
              selectedDate
            )}`}
            onPress={() => {
              navigation.navigate(FACILITIES_SCREENS.CREATE_RESERVATION, {
                id: facility.id,
                name: facility.name,
                maxAmountOfPeople: facility.maxAmountOfPeople,
                maxTimeOfStay: facility.maxTimeOfStay,
                open: facility.open,
                close: facility.close,
                selectedDate,
              });
            }}
          />
        </View>
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  reservationsContainer: {
    marginTop: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.md,
    fontWeight: "bold",
    marginBottom: theme.spacing.md,
  },
  reservationCard: {
    backgroundColor: theme.colors.primaryLight,
    padding: theme.spacing.sm,
    borderRadius: theme.radii.md,
    marginBottom: theme.spacing.sm,
  },
  facilityName: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.primaryDark,
  },
  timeRange: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray700,
    marginTop: theme.spacing.xs,
  },
  noReservations: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginTop: theme.spacing.md,
  },
  dateInfo: {
    alignItems: "center",
    marginBottom: theme.spacing.md,
    marginRight: theme.spacing.md,
  },
  dayNumber: {
    fontSize: 48,
    fontWeight: "bold",
    color: theme.colors.primaryDark,
  },
  dayName: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray700,
    textTransform: "capitalize",
    marginTop: theme.spacing.xs,
  },
  reservationsCount: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginTop: theme.spacing.xs,
  },
  buttonContainer: {
    marginTop: theme.spacing.md,
  },
});
