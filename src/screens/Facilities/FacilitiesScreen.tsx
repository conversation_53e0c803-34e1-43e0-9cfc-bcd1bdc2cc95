import { Card } from "../../components/main/Card";
import { Image, StyleSheet, Text, TouchableOpacity } from "react-native";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Facility } from "../../interfaces/facility";
import { theme } from "../../theme";
import { Col } from "../../components/main/Col";
import { Title } from "../../components/main/Title";
import { Row } from "../../components/main/Row";
import { AntDesign } from "@expo/vector-icons";
import { getStringTime, shortDaysOfWeek } from "../../utils/date-time.utils";
import { GradientView } from "../../components/layouts/GradientView";
import { Loading } from "../../components/Loading";
import { QUERIES } from "../../constants/queries";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { FacilitiesStackParamList } from "../../navigation/types";
import { FACILITIES_SCREENS } from "../../navigation/constants";

export const FacilitiesScreen: React.FC = () => {
  const { data, isLoading } = useCachedQuery<Facility[]>(QUERIES.FACILITIES);

  const navigation =
    useNavigation<NativeStackNavigationProp<FacilitiesStackParamList>>();

  if (isLoading) {
    return <Loading />;
  }

  const facilities = data ?? [];

  return (
    <GradientView firstLineText="Amenidades">
      {facilities.map((facility) => (
        <TouchableOpacity
          key={facility.id}
          onPress={() => {
            navigation.navigate(FACILITIES_SCREENS.FACILITY_DETAIL, {
              facility,
            });
          }}
        >
          <Card>
            <Image source={{ uri: facility.imagePath }} style={styles.image} />
            <Col style={styles.content}>
              <Title size="ss" style={{ color: theme.colors.primaryDark }}>
                {facility.name}
              </Title>
              <Text style={styles.subtitle}>General: </Text>
              <Row style={styles.scheduleContainer}>
                <AntDesign
                  name="clockcircleo"
                  size={theme.fontSizes.xs}
                  color={theme.colors.primaryDark}
                />
                <Text style={styles.schedule}>
                  {getStringTime(facility.open)} -{" "}
                  {getStringTime(facility.close)}
                </Text>
              </Row>

              <Col>
                <Text style={styles.subtitle}>Reservas: </Text>
                <Row style={styles.scheduleContainer}>
                  <AntDesign
                    name="clockcircleo"
                    size={theme.fontSizes.xs}
                    color={theme.colors.primaryDark}
                  />
                  <Text style={styles.schedule}>
                    {facility.startTime
                      ? getStringTime(facility.startTime)
                      : "N/A"}{" "}
                    -{" "}
                    {facility.startTime
                      ? getStringTime(facility.startTime)
                      : "N/A"}
                  </Text>
                </Row>
                <Row style={styles.scheduleContainer}>
                  <AntDesign
                    name="calendar"
                    size={theme.fontSizes.xs}
                    color={theme.colors.primaryDark}
                  />
                  <Text style={styles.schedule}>
                    {facility.daysOfWeek.length
                      ? facility.daysOfWeek
                          .map((day) => shortDaysOfWeek[day])
                          .join(", ")
                      : "N/A"}
                    .
                  </Text>
                </Row>
              </Col>
            </Col>
          </Card>
        </TouchableOpacity>
      ))}
    </GradientView>
  );
};

const styles = StyleSheet.create({
  mainContent: {
    borderTopRightRadius: 32,
    borderTopLeftRadius: 32,
    backgroundColor: "white",
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 5,
  },
  image: {
    width: "33%",
    borderRadius: theme.radii.md,
    height: 100,
  },
  content: {
    width: "67%",
    marginLeft: theme.spacing.md,
    marginRight: theme.spacing.md,
  },
  subtitle: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "500",
    color: theme.colors.secondaryDark,
    marginTop: theme.spacing.xs,
  },
  text: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray700,
    marginTop: theme.spacing.xs,
  },
  scheduleContainer: {
    flex: 1,
    alignItems: "flex-end",
  },
  schedule: {
    fontSize: theme.fontSizes.xs,
    marginLeft: theme.spacing.xs,
    color: theme.colors.gray700,
  },
});
