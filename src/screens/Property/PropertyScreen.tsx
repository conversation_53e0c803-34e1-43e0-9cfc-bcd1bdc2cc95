// src/screens/PropertyScreen.tsx
import { StyleSheet, Text } from "react-native";
import { Picker } from "@react-native-picker/picker";
import { useState } from "react";
import { ResidentsSection } from "../../components/Sections/ResidentsSection";
import { ParkingSpotsSection } from "../../components/Sections/ParkingSpotsSection";
import { PropertyType } from "../../interfaces/property";
import { Col, Row } from "../../components";
import { VehiclesSection } from "../../components/Sections/VehiclesSection";
import { FinesInfractionsPill } from "../../components/FinePill";
import { GradientView } from "../../components/layouts/GradientView";
import { theme, colors } from "../../theme/theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";

export const PropertyScreen: React.FC = () => {
  const { data: userData, error, isLoading } = useCachedQuery<Me>(`mobile/me`);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(
    null
  );

  if (isLoading) return <Text>Cargando</Text>;
  if (error) return <Text>Error</Text>;
  if (!userData) return <Text>Sin datos</Text>;

  const properties = userData.properties || [];

  const selectedProperty =
    properties.length === 1
      ? properties[0]
      : properties.find((p) => p.id === selectedPropertyId);

  return (
    <GradientView firstLineText="Mi propiedad">
      {properties.length > 1 && (
        <Picker
          selectedValue={selectedPropertyId}
          onValueChange={(itemValue) => setSelectedPropertyId(itemValue)}
        >
          {properties.map((p) => (
            <Picker.Item label={p.address} value={p.id} key={p.id} />
          ))}
        </Picker>
      )}

      {selectedProperty && (
        <Col>
          <Text style={{ fontWeight: "bold" }}>
            {selectedProperty.type === PropertyType.DEPARTMENT
              ? "Departamento"
              : "Casa"}{" "}
            {selectedProperty.address ?? "Sin dirección"}
          </Text>
          <Row style={styles.finesInfractionsContainer}>
            <FinesInfractionsPill
              text="Multas"
              total={selectedProperty.fines.length}
              color={theme.colors.red.dark}
            />
            <FinesInfractionsPill
              text="Infracciones"
              total={selectedProperty.infractions.length}
              color={theme.colors.orange.dark}
            />
          </Row>

          <ResidentsSection residents={selectedProperty.residents ?? []} />
          <ParkingSpotsSection
            parkingSpots={selectedProperty.parkingSpots ?? []}
          />
          {/* <PetList pets={selectedProperty.pets ?? []} /> */}
          <VehiclesSection vehicles={selectedProperty.vehicles ?? []} />
          {/* <TagList tags={selectedProperty.tags ?? []} /> */}
        </Col>
      )}
    </GradientView>
  );
};

const styles = StyleSheet.create({
  finesInfractionsContainer: {
    marginTop: theme.spacing.md,
  },
});
