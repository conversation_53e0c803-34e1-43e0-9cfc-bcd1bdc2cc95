import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { CreateComplaint, Complaint } from "../interfaces/complaint";
import { ComplaintType } from "../interfaces/complaint-type";

export const useComplaints = () => {
  const queryClient = useQueryClient();

  // Obtener tipos de queja
  const complaintTypes = useQuery({
    queryKey: ["complaint-types"],
    queryFn: async () => {
      const response = await hoaClient.get<ComplaintType[]>("/mobile/complaint-types");
      return response.data;
    },
  });

  // Crear una nueva queja
  const createComplaint = useMutation({
    mutationFn: async (data: CreateComplaint) => {
      const response = await hoaClient.post<Complaint>("/mobile/complaints", data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["complaints"] });
      queryClient.invalidateQueries({ queryKey: ["me"] });
    },
  });

  return {
    complaintTypes,
    createComplaint,
  };
};
