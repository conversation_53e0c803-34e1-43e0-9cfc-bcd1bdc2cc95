# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2020.                                    *
#  *     Distributed under the Boost Software License, Version 1.0. (See      *
#  *     accompanying file LICENSE_1_0.txt or copy at                         *
#  *     http://www.boost.org/LICENSE_1_0.txt)                                *
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_ARITHMETIC_DETAIL_IS_MINIMUM_NUMBER_HPP
# define BOOST_PREPROCESSOR_ARITHMETIC_DETAIL_IS_MINIMUM_NUMBER_HPP
#
# /* BOOST_PP_DETAIL_IS_MINIMUM_NUMBER */
#
# include <boost/preprocessor/logical/not.hpp>
#
# define BOOST_PP_DETAIL_IS_MINIMUM_NUMBER(x) BOOST_PP_NOT(x)
#
# endif
