# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_ARITHMETIC_DEC_512_HPP
# define BOOST_PREPROCESSOR_ARITHMETIC_DEC_512_HPP
#
# define BOOST_PP_DEC_258 257
# define BOOST_PP_DEC_259 258
# define BOOST_PP_DEC_260 259
# define BOOST_PP_DEC_261 260
# define BOOST_PP_DEC_262 261
# define BOOST_PP_DEC_263 262
# define BOOST_PP_DEC_264 263
# define BOOST_PP_DEC_265 264
# define BOOST_PP_DEC_266 265
# define BOOST_PP_DEC_267 266
# define BOOST_PP_DEC_268 267
# define BOOST_PP_DEC_269 268
# define BOOST_PP_DEC_270 269
# define BOOST_PP_DEC_271 270
# define BOOST_PP_DEC_272 271
# define BOOST_PP_DEC_273 272
# define BOOST_PP_DEC_274 273
# define BOOST_PP_DEC_275 274
# define BOOST_PP_DEC_276 275
# define BOOST_PP_DEC_277 276
# define BOOST_PP_DEC_278 277
# define BOOST_PP_DEC_279 278
# define BOOST_PP_DEC_280 279
# define BOOST_PP_DEC_281 280
# define BOOST_PP_DEC_282 281
# define BOOST_PP_DEC_283 282
# define BOOST_PP_DEC_284 283
# define BOOST_PP_DEC_285 284
# define BOOST_PP_DEC_286 285
# define BOOST_PP_DEC_287 286
# define BOOST_PP_DEC_288 287
# define BOOST_PP_DEC_289 288
# define BOOST_PP_DEC_290 289
# define BOOST_PP_DEC_291 290
# define BOOST_PP_DEC_292 291
# define BOOST_PP_DEC_293 292
# define BOOST_PP_DEC_294 293
# define BOOST_PP_DEC_295 294
# define BOOST_PP_DEC_296 295
# define BOOST_PP_DEC_297 296
# define BOOST_PP_DEC_298 297
# define BOOST_PP_DEC_299 298
# define BOOST_PP_DEC_300 299
# define BOOST_PP_DEC_301 300
# define BOOST_PP_DEC_302 301
# define BOOST_PP_DEC_303 302
# define BOOST_PP_DEC_304 303
# define BOOST_PP_DEC_305 304
# define BOOST_PP_DEC_306 305
# define BOOST_PP_DEC_307 306
# define BOOST_PP_DEC_308 307
# define BOOST_PP_DEC_309 308
# define BOOST_PP_DEC_310 309
# define BOOST_PP_DEC_311 310
# define BOOST_PP_DEC_312 311
# define BOOST_PP_DEC_313 312
# define BOOST_PP_DEC_314 313
# define BOOST_PP_DEC_315 314
# define BOOST_PP_DEC_316 315
# define BOOST_PP_DEC_317 316
# define BOOST_PP_DEC_318 317
# define BOOST_PP_DEC_319 318
# define BOOST_PP_DEC_320 319
# define BOOST_PP_DEC_321 320
# define BOOST_PP_DEC_322 321
# define BOOST_PP_DEC_323 322
# define BOOST_PP_DEC_324 323
# define BOOST_PP_DEC_325 324
# define BOOST_PP_DEC_326 325
# define BOOST_PP_DEC_327 326
# define BOOST_PP_DEC_328 327
# define BOOST_PP_DEC_329 328
# define BOOST_PP_DEC_330 329
# define BOOST_PP_DEC_331 330
# define BOOST_PP_DEC_332 331
# define BOOST_PP_DEC_333 332
# define BOOST_PP_DEC_334 333
# define BOOST_PP_DEC_335 334
# define BOOST_PP_DEC_336 335
# define BOOST_PP_DEC_337 336
# define BOOST_PP_DEC_338 337
# define BOOST_PP_DEC_339 338
# define BOOST_PP_DEC_340 339
# define BOOST_PP_DEC_341 340
# define BOOST_PP_DEC_342 341
# define BOOST_PP_DEC_343 342
# define BOOST_PP_DEC_344 343
# define BOOST_PP_DEC_345 344
# define BOOST_PP_DEC_346 345
# define BOOST_PP_DEC_347 346
# define BOOST_PP_DEC_348 347
# define BOOST_PP_DEC_349 348
# define BOOST_PP_DEC_350 349
# define BOOST_PP_DEC_351 350
# define BOOST_PP_DEC_352 351
# define BOOST_PP_DEC_353 352
# define BOOST_PP_DEC_354 353
# define BOOST_PP_DEC_355 354
# define BOOST_PP_DEC_356 355
# define BOOST_PP_DEC_357 356
# define BOOST_PP_DEC_358 357
# define BOOST_PP_DEC_359 358
# define BOOST_PP_DEC_360 359
# define BOOST_PP_DEC_361 360
# define BOOST_PP_DEC_362 361
# define BOOST_PP_DEC_363 362
# define BOOST_PP_DEC_364 363
# define BOOST_PP_DEC_365 364
# define BOOST_PP_DEC_366 365
# define BOOST_PP_DEC_367 366
# define BOOST_PP_DEC_368 367
# define BOOST_PP_DEC_369 368
# define BOOST_PP_DEC_370 369
# define BOOST_PP_DEC_371 370
# define BOOST_PP_DEC_372 371
# define BOOST_PP_DEC_373 372
# define BOOST_PP_DEC_374 373
# define BOOST_PP_DEC_375 374
# define BOOST_PP_DEC_376 375
# define BOOST_PP_DEC_377 376
# define BOOST_PP_DEC_378 377
# define BOOST_PP_DEC_379 378
# define BOOST_PP_DEC_380 379
# define BOOST_PP_DEC_381 380
# define BOOST_PP_DEC_382 381
# define BOOST_PP_DEC_383 382
# define BOOST_PP_DEC_384 383
# define BOOST_PP_DEC_385 384
# define BOOST_PP_DEC_386 385
# define BOOST_PP_DEC_387 386
# define BOOST_PP_DEC_388 387
# define BOOST_PP_DEC_389 388
# define BOOST_PP_DEC_390 389
# define BOOST_PP_DEC_391 390
# define BOOST_PP_DEC_392 391
# define BOOST_PP_DEC_393 392
# define BOOST_PP_DEC_394 393
# define BOOST_PP_DEC_395 394
# define BOOST_PP_DEC_396 395
# define BOOST_PP_DEC_397 396
# define BOOST_PP_DEC_398 397
# define BOOST_PP_DEC_399 398
# define BOOST_PP_DEC_400 399
# define BOOST_PP_DEC_401 400
# define BOOST_PP_DEC_402 401
# define BOOST_PP_DEC_403 402
# define BOOST_PP_DEC_404 403
# define BOOST_PP_DEC_405 404
# define BOOST_PP_DEC_406 405
# define BOOST_PP_DEC_407 406
# define BOOST_PP_DEC_408 407
# define BOOST_PP_DEC_409 408
# define BOOST_PP_DEC_410 409
# define BOOST_PP_DEC_411 410
# define BOOST_PP_DEC_412 411
# define BOOST_PP_DEC_413 412
# define BOOST_PP_DEC_414 413
# define BOOST_PP_DEC_415 414
# define BOOST_PP_DEC_416 415
# define BOOST_PP_DEC_417 416
# define BOOST_PP_DEC_418 417
# define BOOST_PP_DEC_419 418
# define BOOST_PP_DEC_420 419
# define BOOST_PP_DEC_421 420
# define BOOST_PP_DEC_422 421
# define BOOST_PP_DEC_423 422
# define BOOST_PP_DEC_424 423
# define BOOST_PP_DEC_425 424
# define BOOST_PP_DEC_426 425
# define BOOST_PP_DEC_427 426
# define BOOST_PP_DEC_428 427
# define BOOST_PP_DEC_429 428
# define BOOST_PP_DEC_430 429
# define BOOST_PP_DEC_431 430
# define BOOST_PP_DEC_432 431
# define BOOST_PP_DEC_433 432
# define BOOST_PP_DEC_434 433
# define BOOST_PP_DEC_435 434
# define BOOST_PP_DEC_436 435
# define BOOST_PP_DEC_437 436
# define BOOST_PP_DEC_438 437
# define BOOST_PP_DEC_439 438
# define BOOST_PP_DEC_440 439
# define BOOST_PP_DEC_441 440
# define BOOST_PP_DEC_442 441
# define BOOST_PP_DEC_443 442
# define BOOST_PP_DEC_444 443
# define BOOST_PP_DEC_445 444
# define BOOST_PP_DEC_446 445
# define BOOST_PP_DEC_447 446
# define BOOST_PP_DEC_448 447
# define BOOST_PP_DEC_449 448
# define BOOST_PP_DEC_450 449
# define BOOST_PP_DEC_451 450
# define BOOST_PP_DEC_452 451
# define BOOST_PP_DEC_453 452
# define BOOST_PP_DEC_454 453
# define BOOST_PP_DEC_455 454
# define BOOST_PP_DEC_456 455
# define BOOST_PP_DEC_457 456
# define BOOST_PP_DEC_458 457
# define BOOST_PP_DEC_459 458
# define BOOST_PP_DEC_460 459
# define BOOST_PP_DEC_461 460
# define BOOST_PP_DEC_462 461
# define BOOST_PP_DEC_463 462
# define BOOST_PP_DEC_464 463
# define BOOST_PP_DEC_465 464
# define BOOST_PP_DEC_466 465
# define BOOST_PP_DEC_467 466
# define BOOST_PP_DEC_468 467
# define BOOST_PP_DEC_469 468
# define BOOST_PP_DEC_470 469
# define BOOST_PP_DEC_471 470
# define BOOST_PP_DEC_472 471
# define BOOST_PP_DEC_473 472
# define BOOST_PP_DEC_474 473
# define BOOST_PP_DEC_475 474
# define BOOST_PP_DEC_476 475
# define BOOST_PP_DEC_477 476
# define BOOST_PP_DEC_478 477
# define BOOST_PP_DEC_479 478
# define BOOST_PP_DEC_480 479
# define BOOST_PP_DEC_481 480
# define BOOST_PP_DEC_482 481
# define BOOST_PP_DEC_483 482
# define BOOST_PP_DEC_484 483
# define BOOST_PP_DEC_485 484
# define BOOST_PP_DEC_486 485
# define BOOST_PP_DEC_487 486
# define BOOST_PP_DEC_488 487
# define BOOST_PP_DEC_489 488
# define BOOST_PP_DEC_490 489
# define BOOST_PP_DEC_491 490
# define BOOST_PP_DEC_492 491
# define BOOST_PP_DEC_493 492
# define BOOST_PP_DEC_494 493
# define BOOST_PP_DEC_495 494
# define BOOST_PP_DEC_496 495
# define BOOST_PP_DEC_497 496
# define BOOST_PP_DEC_498 497
# define BOOST_PP_DEC_499 498
# define BOOST_PP_DEC_500 499
# define BOOST_PP_DEC_501 500
# define BOOST_PP_DEC_502 501
# define BOOST_PP_DEC_503 502
# define BOOST_PP_DEC_504 503
# define BOOST_PP_DEC_505 504
# define BOOST_PP_DEC_506 505
# define BOOST_PP_DEC_507 506
# define BOOST_PP_DEC_508 507
# define BOOST_PP_DEC_509 508
# define BOOST_PP_DEC_510 509
# define BOOST_PP_DEC_511 510
# define BOOST_PP_DEC_512 511
# define BOOST_PP_DEC_513 512
#
# endif
