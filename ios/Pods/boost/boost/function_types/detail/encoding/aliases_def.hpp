
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

// no include guards, this file is intended for multiple inclusions

#define callable_builtin BOOST_FT_callable_builtin
#define member           BOOST_FT_member_pointer
#define non_member       BOOST_FT_non_member
#define variadic         BOOST_FT_variadic
#define non_variadic     BOOST_FT_non_variadic

